<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.4.0</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>com.paycraft.rta.abt.cam</groupId>
    <artifactId>RTA-ABT-CAM-TibcoExternalService</artifactId>
    <version>2.0.0-SNAPSHOT</version>
    <name>tibco-external-service</name>
    <description>Tibco External Service</description>

    <properties>
        <java.version>17</java.version>
        <org.mapstruct.version>1.5.5.Final</org.mapstruct.version>
        <lombok.version>1.18.24</lombok.version>
    </properties>

    <dependencies>
        <!-- Spring Boot Dependencies -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
            <version>1.15.2</version>
        </dependency>

        <!-- Micrometer tracing bridge to Brave -->
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-tracing-bridge-brave</artifactId>
        </dependency>

        <!-- Zipkin reporter to send spans to Zipkin -->
        <dependency>
            <groupId>io.zipkin.reporter2</groupId>
            <artifactId>zipkin-reporter-brave</artifactId>
        </dependency>
        <dependency>
            <groupId>io.zipkin.reporter2</groupId>
            <artifactId>zipkin-sender-urlconnection</artifactId>
        </dependency>




        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents.client5</groupId>
            <artifactId>httpclient5</artifactId>
        </dependency>

        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-httpclient</artifactId>
            <version>13.6</version> <!-- works with Spring Boot 3.4 -->
        </dependency>



        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <!-- OpenFeign for HTTP calls -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <version>4.2.0</version>
        </dependency>

        <!-- Lombok for reducing boilerplate -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <scope>provided</scope>
        </dependency>

        <!-- Oracle JDBC Driver -->
        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc11</artifactId>
            <scope>runtime</scope>
        </dependency>

        <!-- MapStruct for DTO mappings -->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${org.mapstruct.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>${org.mapstruct.version}</version>
        </dependency>

        <!-- Swagger API Documentation -->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
            <version>2.7.0</version>
        </dependency>

        <!-- Spring Validation -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- Logging Dependencies -->
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j-impl</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
        </dependency>

        <!-- Test Dependencies -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.paycraft.rta.abt.cam</groupId>
            <artifactId>RTA-ABT-CAM-COMMON</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <!-- JAXB API for XML binding -->
<!--        <dependency>-->
<!--            <groupId>jakarta.xml.bind</groupId>-->
<!--            <artifactId>jakarta.xml.bind-api</artifactId>-->
<!--        </dependency>-->

        <!-- JAXB Runtime -->
<!--        <dependency>-->
<!--            <groupId>org.glassfish.jaxb</groupId>-->
<!--            <artifactId>jaxb-runtime</artifactId>-->
<!--        </dependency>-->

        <!-- Jakarta Activation (required for JAXB) -->
        <dependency>
            <groupId>jakarta.activation</groupId>
            <artifactId>jakarta.activation-api</artifactId>
            <version>2.1.0</version>
        </dependency>

        <!-- Maven dependencies -->
        <dependency>
            <groupId>org.springframework.ws</groupId>
            <artifactId>spring-ws-core</artifactId>
            <version>4.0.5</version> <!-- Use latest -->
        </dependency>


        <!-- Jakarta XML Bind API -->
        <dependency>
            <groupId>jakarta.xml.bind</groupId>
            <artifactId>jakarta.xml.bind-api</artifactId>
            <version>4.0.0</version>
        </dependency>
        <!-- JAXB Runtime -->
        <dependency>
            <groupId>org.glassfish.jaxb</groupId>
            <artifactId>jaxb-runtime</artifactId>
            <version>4.0.3</version>
        </dependency>
        <!-- Jakarta XML WS API -->
        <dependency>
            <groupId>jakarta.xml.ws</groupId>
            <artifactId>jakarta.xml.ws-api</artifactId>
            <version>4.0.0</version>
        </dependency>
        <dependency>
            <groupId>jakarta.jws</groupId>
            <artifactId>jakarta.jws-api</artifactId>
            <version>3.0.0</version>
        </dependency>
        <!-- JAX-WS Runtime -->
        <dependency>
            <groupId>com.sun.xml.ws</groupId>
            <artifactId>jaxws-rt</artifactId>
            <version>4.0.1</version>
        </dependency>
        <!-- JAX-WS Tools (includes wsimport) -->
        <dependency>
            <groupId>com.sun.xml.ws</groupId>
            <artifactId>jaxws-tools</artifactId>
            <version>4.0.1</version>
        </dependency>
        <!-- JAXB XJC (for additional schema processing) -->
        <dependency>
            <groupId>org.glassfish.jaxb</groupId>
            <artifactId>jaxb-xjc</artifactId>
            <version>4.0.3</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.ws</groupId>
            <artifactId>spring-ws-security</artifactId>
            <version>4.0.5</version>
        </dependency>

        <!-- WSS4J (Web Services Security for Java) -->
        <dependency>
            <groupId>org.apache.wss4j</groupId>
            <artifactId>wss4j-ws-security-dom</artifactId>
            <version>3.0.3</version>
        </dependency>

        <!-- Additional WSS4J dependencies -->
        <dependency>
            <groupId>org.apache.wss4j</groupId>
            <artifactId>wss4j-ws-security-common</artifactId>
            <version>3.0.3</version>
        </dependency>

        <dependency>
            <groupId>org.apache.wss4j</groupId>
            <artifactId>wss4j-ws-security-stax</artifactId>
            <version>3.0.3</version>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.14</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>org.apache.httpcomponents.client5</groupId>-->
<!--            <artifactId>httpclient5</artifactId>-->
<!--            <version>5.3.1</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.apache.httpcomponents.core5</groupId>
            <artifactId>httpcore5</artifactId>
            <version>5.3.1</version>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents.client5</groupId>
            <artifactId>httpclient5-fluent</artifactId>
            <version>5.3.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-core</artifactId>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <!-- Maven Compiler Plugin Configuration -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.10.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.2.2</version>
                <configuration>
                    <archive>
                        <manifest>
                            <addClasspath>true</addClasspath>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>3.4.0</version>
                <configuration>
                    <mainClass>com.paycraft.rta.abt.cam.external_integration.ExternalIntegrationApplication</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

<!--            <plugin>-->
<!--            <groupId>org.codehaus.mojo</groupId>-->
<!--            <artifactId>jaxb2-maven-plugin</artifactId>-->
<!--            <version>3.1.0</version>-->
<!--            <executions>-->
<!--&lt;!&ndash;            <execution>&ndash;&gt;-->
<!--&lt;!&ndash;                <id>xjc-tibco-sms</id>&ndash;&gt;-->
<!--&lt;!&ndash;                <goals>&ndash;&gt;-->
<!--&lt;!&ndash;                    <goal>xjc</goal>&ndash;&gt;-->
<!--&lt;!&ndash;                </goals>&ndash;&gt;-->
<!--&lt;!&ndash;                <configuration>&ndash;&gt;-->
<!--&lt;!&ndash;                    &lt;!&ndash; Enable WSDL processing &ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;                    &lt;!&ndash;<extension>true</extension>&ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;                    <arguments>&ndash;&gt;-->
<!--&lt;!&ndash;                        <argument>-wsdl</argument>&ndash;&gt;-->
<!--&lt;!&ndash;                        <argument>-verbose</argument>&ndash;&gt;-->
<!--&lt;!&ndash;                        &lt;!&ndash;<argument>-debug</argument>&ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;                    </arguments>&ndash;&gt;-->
<!--&lt;!&ndash;                    &lt;!&ndash; WSDL source directory &ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;                    <sources>&ndash;&gt;-->
<!--&lt;!&ndash;                        <source>${project.basedir}/src/main/resources/wsdl/tibco_sms.wsdl</source>&ndash;&gt;-->
<!--&lt;!&ndash;                    </sources>&ndash;&gt;-->
<!--&lt;!&ndash;                    &lt;!&ndash; Package name for generated classes &ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;                    <packageName>com.paycraft.rta.abt.cam.external_integration.xml.tibco_sms.dto</packageName>&ndash;&gt;-->
<!--&lt;!&ndash;                    &lt;!&ndash; Output directory for generated classes &ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;                    <outputDirectory>${project.build.directory}/generated-sources/jaxb/tibco_sms</outputDirectory>&ndash;&gt;-->
<!--&lt;!&ndash;                    &lt;!&ndash; Clear output directory before generating &ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;                    &lt;!&ndash;<clearOutputDir>true</clearOutputDir>&ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;                </configuration>&ndash;&gt;-->
<!--&lt;!&ndash;            </execution>&ndash;&gt;-->

<!--&lt;!&ndash;                <execution>&ndash;&gt;-->
<!--&lt;!&ndash;                    <id>xjc-tibco-email</id>&ndash;&gt;-->
<!--&lt;!&ndash;                    <goals>&ndash;&gt;-->
<!--&lt;!&ndash;                        <goal>xjc</goal>&ndash;&gt;-->
<!--&lt;!&ndash;                    </goals>&ndash;&gt;-->
<!--&lt;!&ndash;                    <configuration>&ndash;&gt;-->
<!--&lt;!&ndash;                        &lt;!&ndash; Enable WSDL processing &ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;                        &lt;!&ndash;<extension>true</extension>&ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;                        <arguments>&ndash;&gt;-->
<!--&lt;!&ndash;                            <argument>-wsdl</argument>&ndash;&gt;-->
<!--&lt;!&ndash;                            <argument>-verbose</argument>&ndash;&gt;-->
<!--&lt;!&ndash;                            &lt;!&ndash;<argument>-debug</argument>&ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;                        </arguments>&ndash;&gt;-->
<!--&lt;!&ndash;                        &lt;!&ndash; WSDL source directory &ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;                        <sources>&ndash;&gt;-->
<!--&lt;!&ndash;                            <source>${project.basedir}/src/main/resources/wsdl/tibco_email.wsdl</source>&ndash;&gt;-->
<!--&lt;!&ndash;                        </sources>&ndash;&gt;-->
<!--&lt;!&ndash;                        &lt;!&ndash; Package name for generated classes &ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;                        <packageName>com.paycraft.rta.abt.cam.external_integration.xml.tibco_email.dto</packageName>&ndash;&gt;-->
<!--&lt;!&ndash;                        &lt;!&ndash; Output directory for generated classes &ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;                        <outputDirectory>${project.build.directory}/generated-sources/jaxb/tibco_email</outputDirectory>&ndash;&gt;-->
<!--&lt;!&ndash;                        &lt;!&ndash; Clear output directory before generating &ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;                        &lt;!&ndash;<clearOutputDir>true</clearOutputDir>&ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;                    </configuration>&ndash;&gt;-->
<!--&lt;!&ndash;                </execution>&ndash;&gt;-->

<!--                <execution>-->
<!--                    <id>xjc-epay-service</id>-->
<!--                    <goals>-->
<!--                        <goal>xjc</goal>-->
<!--                    </goals>-->
<!--                    <configuration>-->
<!--                        &lt;!&ndash; Enable WSDL processing &ndash;&gt;-->
<!--                        &lt;!&ndash;<extension>true</extension>&ndash;&gt;-->
<!--                        <arguments>-->
<!--                            <argument>-wsdl</argument>-->
<!--                            <argument>-verbose</argument>-->
<!--                            &lt;!&ndash;<argument>-debug</argument>&ndash;&gt;-->
<!--                        </arguments>-->
<!--                        &lt;!&ndash; WSDL source directory &ndash;&gt;-->
<!--                        <sources>-->
<!--                            <source>${project.basedir}/src/main/resources/wsdl/epay_service.wsdl</source>-->
<!--                        </sources>-->
<!--                        &lt;!&ndash; Package name for generated classes &ndash;&gt;-->
<!--                        <packageName>com.paycraft.rta.abt.cam.external_integration.xml.epay_service.dto</packageName>-->
<!--                        &lt;!&ndash; Output directory for generated classes &ndash;&gt;-->
<!--                        <outputDirectory>${project.build.directory}/generated-sources/jaxb/epay_service</outputDirectory>-->
<!--                        &lt;!&ndash; Clear output directory before generating &ndash;&gt;-->
<!--                        &lt;!&ndash;<clearOutputDir>true</clearOutputDir>&ndash;&gt;-->
<!--                    </configuration>-->
<!--                </execution>-->

<!--                <execution>-->
<!--                    <id>xjc-epay-transaction-service</id>-->
<!--                    <goals>-->
<!--                        <goal>xjc</goal>-->
<!--                    </goals>-->
<!--                    <configuration>-->
<!--                        &lt;!&ndash; Enable WSDL processing &ndash;&gt;-->
<!--                        &lt;!&ndash;<extension>true</extension>&ndash;&gt;-->
<!--                        <arguments>-->
<!--                            <argument>-wsdl</argument>-->
<!--                            <argument>-verbose</argument>-->
<!--                            &lt;!&ndash;<argument>-debug</argument>&ndash;&gt;-->
<!--                        </arguments>-->
<!--                        &lt;!&ndash; WSDL source directory &ndash;&gt;-->
<!--                        <sources>-->
<!--                            <source>${project.basedir}/src/main/resources/wsdl/epay_transaction_service.wsdl</source>-->
<!--                        </sources>-->
<!--                        &lt;!&ndash; Package name for generated classes &ndash;&gt;-->
<!--                        <packageName>com.paycraft.rta.abt.cam.external_integration.xml.epay_transaction_service.dto</packageName>-->
<!--                        &lt;!&ndash; Output directory for generated classes &ndash;&gt;-->
<!--                        <outputDirectory>${project.build.directory}/generated-sources/jaxb/epay_transaction_service</outputDirectory>-->
<!--                        &lt;!&ndash; Clear output directory before generating &ndash;&gt;-->
<!--                        &lt;!&ndash;<clearOutputDir>true</clearOutputDir>&ndash;&gt;-->
<!--                    </configuration>-->
<!--                </execution>-->
<!--            </executions>-->
<!--            </plugin>-->


        </plugins>
    </build>

</project>
