<?xml version="1.0" encoding="UTF-8"?>
<!--Created by TIBCO WSDL-->
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:tns="http://www.rta.ae/ActiveMatrix/ESB/ePayStatusService/1_0" xmlns:ns0="http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/ePayStatusSchema.xsd" xmlns:ns2="http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/ePayStatusCommonSchema.xsd" xmlns:ns1="http://www.rta.ae/ActiveMatrix/ESB/ePayStatusService/Fault/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" name="Untitled" targetNamespace="http://www.rta.ae/ActiveMatrix/ESB/ePayStatusService/1_0">
    <wsdl:types>
        <xs:schema xmlns="http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/ePayStatusCommonSchema.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/ePayStatusCommonSchema.xsd" elementFormDefault="qualified" attributeFormDefault="unqualified">
            <xs:simpleType name="degTrnType">
                <xs:restriction base="xs:string"/>
            </xs:simpleType>
            <xs:simpleType name="pgCodeType">
                <xs:restriction base="xs:string"/>
            </xs:simpleType>
            <xs:simpleType name="pgTrnType">
                <xs:restriction base="xs:string"/>
            </xs:simpleType>
            <xs:simpleType name="resultType">
                <xs:restriction base="xs:string"/>
            </xs:simpleType>
            <xs:simpleType name="servCodeType">
                <xs:restriction base="xs:string"/>
            </xs:simpleType>
            <xs:simpleType name="spCpdeType">
                <xs:restriction base="xs:string"/>
            </xs:simpleType>
            <xs:simpleType name="spTrnType">
                <xs:restriction base="xs:string"/>
            </xs:simpleType>
            <xs:simpleType name="timeOutType">
                <xs:restriction base="xs:string"/>
            </xs:simpleType>
            <xs:element name="degTrn" type="xs:string"/>
            <xs:element name="pgCode" type="xs:string"/>
            <xs:element name="pgTrn" type="xs:string"/>
            <xs:element name="result" type="xs:string"/>
            <xs:element name="servCode" type="xs:string"/>
            <xs:element name="spTrn" type="xs:string"/>
            <xs:element name="timeOut" type="xs:string"/>
        </xs:schema>
        <xs:schema xmlns="http://www.rta.ae/ActiveMatrix/ESB/ePayStatusService/Fault/XMLSchema" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://www.rta.ae/ActiveMatrix/ESB/ePayStatusService/Fault/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
            <xs:element name="Fault">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element ref="ns1:FaultCode"/>
                        <xs:element ref="ns1:FaultString"/>
                        <xs:element ref="ns1:FaultDetail"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="FaultCode" type="xs:string"/>
            <xs:element name="FaultDetail" type="xs:string"/>
            <xs:element name="FaultString" type="xs:string"/>
        </xs:schema>
        <xs:schema xmlns="http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/ePayStatusSchema.xsd" xmlns:ns0="http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/ePayStatusCommonSchema.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/ePayStatusSchema.xsd" elementFormDefault="qualified" attributeFormDefault="unqualified">
            <xs:import namespace="http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/ePayStatusCommonSchema.xsd"/>
            <xs:element name="epayTransactionSearch">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="spCode" type="ns0:spCpdeType"/>
                        <xs:element name="servCode" type="ns0:servCodeType"/>
                        <xs:element name="spTrn" type="ns0:spTrnType"/>
                        <xs:element name="degTrn" type="ns0:degTrnType"/>
                        <xs:element name="pgCode" type="ns0:pgCodeType"/>
                        <xs:element name="pgTrn" type="ns0:pgTrnType"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="epayTransactionSearchResponse">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="result" type="ns0:resultType"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="getDEGTransactionStatus">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="spCode" type="ns0:spCpdeType"/>
                        <xs:element name="servCode" type="ns0:servCodeType"/>
                        <xs:element name="spTrn" type="ns0:spTrnType"/>
                        <xs:element name="timeOut" type="ns0:timeOutType"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="getDEGTransactionStatusResponse">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="result" type="ns0:resultType"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="getTransactionStatus">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="spCode" type="ns0:spCpdeType"/>
                        <xs:element name="servCode" type="ns0:servCodeType"/>
                        <xs:element name="spTrn" type="ns0:spTrnType"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="getTransactionStatusResponse">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="result" type="ns0:resultType"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:schema>
    </wsdl:types>
    <wsdl:service name="ePayTransactionStatusImpl">
        <wsdl:port name="SOAPService_ePayStatusService" binding="tns:SOAPService_ePayStatusService">
            <soap:address location="https://localhost:11162/epaystatus"/>
        </wsdl:port>
    </wsdl:service>
    <wsdl:portType name="ePayTransactionStatusImpl">
        <wsdl:operation name="getTransactionStatus">
            <wsdl:input message="tns:getTransactionStatus"/>
            <wsdl:output message="tns:getTransactionStatusResponse"/>
            <wsdl:fault name="fault" message="tns:Fault"/>
        </wsdl:operation>
        <wsdl:operation name="ePayTransactionSearch">
            <wsdl:input message="tns:ePayTransactionSearch"/>
            <wsdl:output message="tns:ePayTransactionSearchResponse"/>
            <wsdl:fault name="fault" message="tns:Fault"/>
        </wsdl:operation>
        <wsdl:operation name="getDEGTransactionStatus">
            <wsdl:input message="tns:getDEGTransactionStatus"/>
            <wsdl:output message="tns:getDEGTransactionStatusResponse"/>
            <wsdl:fault name="fault" message="tns:Fault"/>
        </wsdl:operation>
    </wsdl:portType>
    <wsdl:binding name="SOAPService_ePayStatusService" type="tns:ePayTransactionStatusImpl">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="getTransactionStatus">
            <soap:operation style="document" soapAction="getTransactionStatus"/>
            <wsdl:input>
                <soap:body use="literal" parts="Request"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" parts="Response"/>
            </wsdl:output>
            <wsdl:fault name="fault">
                <soap:fault use="literal" name="fault"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="ePayTransactionSearch">
            <soap:operation style="document" soapAction="ePayTransactionSearch"/>
            <wsdl:input>
                <soap:body use="literal" parts="Request"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" parts="Response"/>
            </wsdl:output>
            <wsdl:fault name="fault">
                <soap:fault use="literal" name="fault"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getDEGTransactionStatus">
            <soap:operation style="document" soapAction="getDEGTransactionStatus"/>
            <wsdl:input>
                <soap:body use="literal" parts="Request"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" parts="Response"/>
            </wsdl:output>
            <wsdl:fault name="fault">
                <soap:fault use="literal" name="fault"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:message name="getTransactionStatus">
        <wsdl:part name="Request" element="ns0:getTransactionStatus"/>
    </wsdl:message>
    <wsdl:message name="getTransactionStatusResponse">
        <wsdl:part name="Response" element="ns0:getTransactionStatusResponse"/>
    </wsdl:message>
    <wsdl:message name="Fault">
        <wsdl:part name="Fault" element="ns1:Fault"/>
    </wsdl:message>
    <wsdl:message name="ePayTransactionSearch">
        <wsdl:part name="Request" element="ns0:epayTransactionSearch"/>
    </wsdl:message>
    <wsdl:message name="ePayTransactionSearchResponse">
        <wsdl:part name="Response" element="ns0:epayTransactionSearchResponse"/>
    </wsdl:message>
    <wsdl:message name="getDEGTransactionStatus">
        <wsdl:part name="Request" element="ns0:getDEGTransactionStatus"/>
    </wsdl:message>
    <wsdl:message name="getDEGTransactionStatusResponse">
        <wsdl:part name="Response" element="ns0:getDEGTransactionStatusResponse"/>
    </wsdl:message>
</wsdl:definitions>
