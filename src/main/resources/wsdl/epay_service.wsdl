<?xml version="1.0" encoding="UTF-8"?>
<!--Created by TIBCO WSDL-->
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:tns="http://www.rta.ae/ActiveMatrix/ESB/ePayService/1_0" xmlns:ns0="http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/Schema.xsd" xmlns:ns2="http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/CommonTypesSchema.xsd" xmlns:ns1="http://www.rta.ae/ActiveMatrix/ESB/ePayService/Fault/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" name="ePayService" targetNamespace="http://www.rta.ae/ActiveMatrix/ESB/ePayService/1_0">
    <wsdl:types>
        <xsd:schema xmlns="http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/Schema.xsd" xmlns:common="http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/CommonTypesSchema.xsd" xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/Schema.xsd" elementFormDefault="qualified" attributeFormDefault="unqualified">
            <xsd:import namespace="http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/CommonTypesSchema.xsd"/>
            <xsd:complexType name="BeneficiaryInfo">
                <xsd:sequence>
                    <xsd:element name="accountId" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="txnAmount" type="common:Amount" minOccurs="0"/>
                    <xsd:element name="fullNameEn" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="fullNameAr" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="mobileNo" type="common:MobileNo" minOccurs="0"/>
                    <xsd:element name="email" type="common:Email" minOccurs="0"/>
                    <xsd:element name="emiratesId" type="common:EmiratesId" minOccurs="0"/>
                    <xsd:element name="type" type="ns0:BeneficiaryType" minOccurs="0"/>
                    <xsd:element name="companyInfo" type="ns0:CompanyInfo" minOccurs="0"/>
                    <xsd:element name="additionalParams" type="common:Map" minOccurs="0"/>
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="BeneficiaryInfos">
                <xsd:sequence>
                    <xsd:element name="beneficiaryInfo" type="ns0:BeneficiaryInfo" maxOccurs="unbounded"/>
                </xsd:sequence>
            </xsd:complexType>
            <xsd:simpleType name="BeneficiaryType">
                <xsd:restriction base="xsd:string">
                    <xsd:enumeration value="Individual"/>
                    <xsd:enumeration value="Corporate"/>
                    <xsd:enumeration value="Government"/>
                </xsd:restriction>
            </xsd:simpleType>
            <xsd:complexType name="CompanyInfo">
                <xsd:sequence>
                    <xsd:element name="companyNameEn" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="companyNameAr" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="tradeLicenseNumber" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="licenseIssuingAuthority" type="xsd:string" minOccurs="0"/>
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="ConfirmServiceDelivery">
                <xsd:sequence>
                    <xsd:element name="spCode" type="xsd:string"/>
                    <xsd:element name="servCode" type="xsd:string"/>
                    <xsd:element name="sptrn" type="xsd:string"/>
                    <xsd:element name="message" type="common:Message" minOccurs="0"/>
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="ConfirmServiceDeliveryResponse">
                <xsd:sequence>
                    <xsd:element name="error" type="common:Error" minOccurs="0"/>
                </xsd:sequence>
                <xsd:attribute name="valid" type="xsd:boolean"/>
            </xsd:complexType>
            <xsd:complexType name="Echo">
                <xsd:sequence>
                    <xsd:element name="word" type="xsd:string"/>
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="EchoResponse">
                <xsd:sequence>
                    <xsd:element name="word" type="xsd:string"/>
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="GenerateTransactionToken">
                <xsd:sequence>
                    <xsd:element name="transactionInfo" type="ns0:TransactionInfo"/>
                    <xsd:element name="userInfo" type="ns0:UserInfo" minOccurs="0"/>
                    <xsd:element name="serviceInfos" type="ns0:ServiceInfos" minOccurs="0"/>
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="GenerateTransactionTokenResponse">
                <xsd:sequence>
                    <xsd:element name="uri" type="xsd:anyURI" minOccurs="0"/>
                    <xsd:element name="error" type="common:Error" minOccurs="0"/>
                </xsd:sequence>
                <xsd:attribute name="valid" use="required" type="xsd:boolean"/>
            </xsd:complexType>
            <xsd:complexType name="GetResponseTokenDetails">
                <xsd:sequence>
                    <xsd:element name="responseToken" type="xsd:string"/>
                    <xsd:element name="spCode" type="xsd:string"/>
                    <xsd:element name="servCode" type="xsd:string"/>
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="GetResponseTokenDetailsResponse">
                <xsd:sequence>
                    <xsd:element name="spCode" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="servCode" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="sptrn" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="degTrn" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="txnTimestamp" type="xsd:dateTime" minOccurs="0"/>
                    <xsd:element name="paymentMethod" type="ns0:PaymentMethod" minOccurs="0"/>
                    <xsd:element name="message" type="common:Message" minOccurs="0"/>
                    <xsd:element name="error" type="common:Error" minOccurs="0"/>
                </xsd:sequence>
                <xsd:attribute name="valid" type="xsd:boolean"/>
            </xsd:complexType>
            <xsd:simpleType name="PaymentChannel">
                <xsd:restriction base="xsd:string">
                    <xsd:enumeration value="100">
                        <xsd:annotation>
                            <xsd:documentation>
						Online Channel - Entry Point: Web Redirection
					</xsd:documentation>
                        </xsd:annotation>
                    </xsd:enumeration>
                    <xsd:enumeration value="101">
                        <xsd:annotation>
                            <xsd:documentation>
						KIOSK - Entry Point: Web Service Integration
					</xsd:documentation>
                        </xsd:annotation>
                    </xsd:enumeration>
                    <xsd:enumeration value="102">
                        <xsd:annotation>
                            <xsd:documentation>
						KIOSK - Entry Point: Web Service Integration
					</xsd:documentation>
                        </xsd:annotation>
                    </xsd:enumeration>
                    <xsd:enumeration value="103">
                        <xsd:annotation>
                            <xsd:documentation>
						POS - Entry Point: Web Service Integration
					</xsd:documentation>
                        </xsd:annotation>
                    </xsd:enumeration>
                    <xsd:enumeration value="104">
                        <xsd:annotation>
                            <xsd:documentation>
						MOBILE - Entry Point: Web Service Integration
					</xsd:documentation>
                        </xsd:annotation>
                    </xsd:enumeration>
                </xsd:restriction>
            </xsd:simpleType>
            <xsd:simpleType name="PaymentMethod">
                <xsd:restriction base="xsd:string">
                    <xsd:enumeration value="Not Selected"/>
                    <xsd:enumeration value="Credit Card"/>
                    <xsd:enumeration value="Edirham"/>
                    <xsd:enumeration value="Direct Debit"/>
                    <xsd:enumeration value="EdirhamG2"/>
                    <xsd:enumeration value="OneClick Pay"/>
                    <xsd:enumeration value="Wallet"/>
                </xsd:restriction>
            </xsd:simpleType>
            <xsd:simpleType name="PaymentType">
                <xsd:restriction base="xsd:string">
                    <xsd:enumeration value="sale"/>
                    <xsd:enumeration value="authorize"/>
                </xsd:restriction>
            </xsd:simpleType>
            <xsd:complexType name="ServiceInfo">
                <xsd:sequence>
                    <xsd:element name="serviceNameEn" type="xsd:string"/>
                    <xsd:element name="serviceNameAr" type="xsd:string"/>
                    <xsd:element name="serviceId" type="xsd:string"/>
                    <xsd:element name="gessServiceId" type="xsd:string"/>
                    <xsd:element name="beneficiaryInfos" type="ns0:BeneficiaryInfos"/>
                    <xsd:element name="additionalParams" type="common:Map"/>
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="ServiceInfos">
                <xsd:sequence>
                    <xsd:element name="service" type="ns0:ServiceInfo" maxOccurs="unbounded"/>
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="TransactionInfo">
                <xsd:sequence>
                    <xsd:element name="spCode" type="xsd:string"/>
                    <xsd:element name="servCode" type="xsd:string"/>
                    <xsd:element name="sptrn" type="xsd:string"/>
                    <xsd:element name="amount" type="common:Amount"/>
                    <xsd:element name="timestamp" type="xsd:dateTime"/>
                    <xsd:element name="description" type="xsd:string"/>
                    <xsd:element name="type" type="ns0:PaymentType"/>
                    <xsd:element name="versionCode" type="xsd:string"/>
                    <xsd:element name="paymentChannel" type="ns0:PaymentChannel"/>
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="UserInfo">
                <xsd:annotation>
                    <xsd:documentation>User information from the Service Provider
			</xsd:documentation>
                </xsd:annotation>
                <xsd:sequence>
                    <xsd:element name="isAuthenticated" type="xsd:boolean" minOccurs="0"/>
                    <xsd:element name="userId" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="userName" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="fullNameEn" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="fullNameAr" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="mobileNo" type="common:MobileNo" minOccurs="0"/>
                    <xsd:element name="email" type="common:Email" minOccurs="0"/>
                    <xsd:element name="nationalityCode" type="common:CountryCode" minOccurs="0"/>
                    <xsd:element name="emiratesId" type="common:EmiratesId" minOccurs="0"/>
                    <xsd:element name="emirateCode" type="common:EmirateCode" minOccurs="0"/>
                    <xsd:element name="poBox" type="xsd:string" minOccurs="0"/>
                </xsd:sequence>
            </xsd:complexType>
            <xsd:element name="confirmServiceDelivery" type="ns0:ConfirmServiceDelivery"/>
            <xsd:element name="confirmServiceDeliveryResponse" type="ns0:ConfirmServiceDeliveryResponse"/>
            <xsd:element name="echo" type="ns0:Echo"/>
            <xsd:element name="echoResponse" type="ns0:EchoResponse"/>
            <xsd:element name="generateTransactionToken" type="ns0:GenerateTransactionToken"/>
            <xsd:element name="generateTransactionTokenResponse" type="ns0:GenerateTransactionTokenResponse"/>
            <xsd:element name="getResponseTokenDetails" type="ns0:GetResponseTokenDetails"/>
            <xsd:element name="getResponseTokenDetailsResponse" type="ns0:GetResponseTokenDetailsResponse"/>
        </xsd:schema>
        <xs:schema xmlns="http://www.rta.ae/ActiveMatrix/ESB/ePayService/Fault/XMLSchema" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://www.rta.ae/ActiveMatrix/ESB/ePayService/Fault/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
            <xs:element name="Fault">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element ref="ns1:FaultCode"/>
                        <xs:element ref="ns1:FaultString"/>
                        <xs:element ref="ns1:FaultDetail"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="FaultCode" type="xs:string"/>
            <xs:element name="FaultDetail" type="xs:string"/>
            <xs:element name="FaultString" type="xs:string"/>
        </xs:schema>
        <xsd:schema xmlns="http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/CommonTypesSchema.xsd" xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/CommonTypesSchema.xsd" elementFormDefault="qualified" attributeFormDefault="unqualified">
            <xsd:complexType name="Amount">
                <xsd:simpleContent>
                    <xsd:extension base="ns2:AmountValue">
                        <xsd:attribute name="currency" use="required" type="ns2:CurrencyCode"/>
                    </xsd:extension>
                </xsd:simpleContent>
            </xsd:complexType>
            <xsd:simpleType name="AmountValue">
                <xsd:restriction base="xsd:decimal">
                    <xsd:fractionDigits value="2"/>
                </xsd:restriction>
            </xsd:simpleType>
            <xsd:simpleType name="CountryCode">
                <xsd:annotation>
                    <xsd:documentation>
				3-letter country codes, see:
				http://en.wikipedia.org/wiki/ISO_3166-1_alpha-3
			</xsd:documentation>
                </xsd:annotation>
                <xsd:restriction base="xsd:string">
                    <xsd:minLength value="3"/>
                    <xsd:maxLength value="3"/>
                    <xsd:pattern value="[A-Z]{3}"/>
                </xsd:restriction>
            </xsd:simpleType>
            <xsd:simpleType name="CurrencyCode">
                <xsd:annotation>
                    <xsd:documentation>
				3-letter currency code see:
				http://en.wikipedia.org/wiki/ISO_4217
			</xsd:documentation>
                </xsd:annotation>
                <xsd:restriction base="xsd:string">
                    <xsd:minLength value="3"/>
                    <xsd:maxLength value="3"/>
                    <xsd:pattern value="[A-Z]{3}"/>
                </xsd:restriction>
            </xsd:simpleType>
            <xsd:simpleType name="Email">
                <xsd:restriction base="xsd:string">
                    <xsd:pattern value="[^@]+@[^\.]+\..+"/>
                </xsd:restriction>
            </xsd:simpleType>
            <xsd:simpleType name="EmirateCode">
                <xsd:restriction base="xsd:string">
                    <xsd:enumeration value="AUH">
                        <xsd:annotation>
                            <xsd:documentation>Abu Dhabi</xsd:documentation>
                        </xsd:annotation>
                    </xsd:enumeration>
                    <xsd:enumeration value="DXB">
                        <xsd:annotation>
                            <xsd:documentation>Dubai</xsd:documentation>
                        </xsd:annotation>
                    </xsd:enumeration>
                    <xsd:enumeration value="SHJ">
                        <xsd:annotation>
                            <xsd:documentation>Sharjah</xsd:documentation>
                        </xsd:annotation>
                    </xsd:enumeration>
                    <xsd:enumeration value="AJM">
                        <xsd:annotation>
                            <xsd:documentation>Ajman</xsd:documentation>
                        </xsd:annotation>
                    </xsd:enumeration>
                    <xsd:enumeration value="UAQ">
                        <xsd:annotation>
                            <xsd:documentation>Umm Al Quwain</xsd:documentation>
                        </xsd:annotation>
                    </xsd:enumeration>
                    <xsd:enumeration value="RAK">
                        <xsd:annotation>
                            <xsd:documentation>Ras Al Khaimah</xsd:documentation>
                        </xsd:annotation>
                    </xsd:enumeration>
                    <xsd:enumeration value="FUJ">
                        <xsd:annotation>
                            <xsd:documentation>Fujairah</xsd:documentation>
                        </xsd:annotation>
                    </xsd:enumeration>
                </xsd:restriction>
            </xsd:simpleType>
            <xsd:simpleType name="EmiratesId">
                <xsd:restriction base="xsd:string">
                    <xsd:minLength value="15"/>
                    <xsd:maxLength value="15"/>
                </xsd:restriction>
            </xsd:simpleType>
            <xsd:complexType name="Entry">
                <xsd:sequence>
                    <xsd:element name="key" type="xsd:string"/>
                    <xsd:element name="value" type="xsd:string"/>
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="Error">
                <xsd:sequence>
                    <xsd:element name="code" type="xsd:string"/>
                    <xsd:element name="message" type="xsd:string" minOccurs="0"/>
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="Map">
                <xsd:sequence>
                    <xsd:element name="entry" type="ns2:Entry" maxOccurs="unbounded"/>
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="Message">
                <xsd:sequence>
                    <xsd:element name="code" type="xsd:int"/>
                    <xsd:element name="text" type="xsd:string" minOccurs="0"/>
                </xsd:sequence>
            </xsd:complexType>
            <xsd:simpleType name="MobileNo">
                <xsd:restriction base="xsd:string">
                    <xsd:pattern value="\+?([0-9]{2})?([0-9]){7,15}"/>
                </xsd:restriction>
            </xsd:simpleType>
        </xsd:schema>
    </wsdl:types>
    <wsdl:message name="getResponseTokenDetails">
        <wsdl:part name="Request" element="ns0:getResponseTokenDetails"/>
    </wsdl:message>
    <wsdl:message name="getResponseTokenDetailsResponse">
        <wsdl:part name="Request" element="ns0:getResponseTokenDetailsResponse"/>
    </wsdl:message>
    <wsdl:message name="Fault">
        <wsdl:part name="Fault" element="ns1:Fault"/>
    </wsdl:message>
    <wsdl:message name="confirmServiceDelivery">
        <wsdl:part name="Request" element="ns0:confirmServiceDelivery"/>
    </wsdl:message>
    <wsdl:message name="confirmServiceDeliverResponse">
        <wsdl:part name="Request" element="ns0:confirmServiceDeliveryResponse"/>
    </wsdl:message>
    <wsdl:message name="echo">
        <wsdl:part name="Request" element="ns0:echo"/>
    </wsdl:message>
    <wsdl:message name="echoResponse">
        <wsdl:part name="Response" element="ns0:echoResponse"/>
    </wsdl:message>
    <wsdl:message name="generateTransactionToken">
        <wsdl:part name="Request" element="ns0:generateTransactionToken"/>
    </wsdl:message>
    <wsdl:message name="generateTransactionTokenResponse">
        <wsdl:part name="Response" element="ns0:generateTransactionTokenResponse"/>
    </wsdl:message>
    <wsdl:portType name="ePayServiceImpl">
        <wsdl:operation name="getResponseTokenDetails">
            <wsdl:input message="tns:getResponseTokenDetails"/>
            <wsdl:output message="tns:getResponseTokenDetailsResponse"/>
            <wsdl:fault name="fault" message="tns:Fault"/>
        </wsdl:operation>
        <wsdl:operation name="confirmServiceDelivery">
            <wsdl:input message="tns:confirmServiceDelivery"/>
            <wsdl:output message="tns:confirmServiceDeliverResponse"/>
            <wsdl:fault name="fault" message="tns:Fault"/>
        </wsdl:operation>
        <wsdl:operation name="echo">
            <wsdl:input message="tns:echo"/>
            <wsdl:output message="tns:echoResponse"/>
            <wsdl:fault name="fault" message="tns:Fault"/>
        </wsdl:operation>
        <wsdl:operation name="generateTransactionToken">
            <wsdl:input message="tns:generateTransactionToken"/>
            <wsdl:output message="tns:generateTransactionTokenResponse"/>
            <wsdl:fault name="fault" message="tns:Fault"/>
        </wsdl:operation>
    </wsdl:portType>
    <wsdl:binding name="SOAPService_ePay" type="tns:ePayServiceImpl">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="getResponseTokenDetails">
            <wsdl:documentation/>
            <soap:operation style="document" soapAction="getResponseTokenDetails"/>
            <wsdl:input>
                <soap:body use="literal" parts="Request"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" parts="Request"/>
            </wsdl:output>
            <wsdl:fault name="fault">
                <soap:fault use="literal" name="fault"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="confirmServiceDelivery">
            <wsdl:documentation/>
            <soap:operation style="document" soapAction="confirmServiceDelivery"/>
            <wsdl:input>
                <soap:body use="literal" parts="Request"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" parts="Request"/>
            </wsdl:output>
            <wsdl:fault name="fault">
                <soap:fault use="literal" name="fault"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="echo">
            <wsdl:documentation/>
            <soap:operation style="document" soapAction="echo"/>
            <wsdl:input>
                <soap:body use="literal" parts="Request"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" parts="Response"/>
            </wsdl:output>
            <wsdl:fault name="fault">
                <soap:fault use="literal" name="fault"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="generateTransactionToken">
            <wsdl:documentation/>
            <soap:operation style="document" soapAction="generateTransactionToken"/>
            <wsdl:input>
                <soap:body use="literal" parts="Request"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" parts="Response"/>
            </wsdl:output>
            <wsdl:fault name="fault">
                <soap:fault use="literal" name="fault"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:service name="ePayService">
        <wsdl:port name="SOAPService_ePay" binding="tns:SOAPService_ePay">
            <soap:address location="https://eipstg.rtatestdom.local:12115/epayservice"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>