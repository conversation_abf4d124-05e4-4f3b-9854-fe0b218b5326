property.logPattern = %d{yyyy-MM-dd HH:mm:ss:SSS} | %X{X-Client-Request-Identifier} | [%t] %-5p [%4L : %-30c{1}] - %m%n
property.filename = rta-abt-cam-TibcoExternalIntegration

appenders = file, console
# console appender configuration
appender.console.name = console
appender.console.type = Console
appender.console.layout.type = PatternLayout
appender.console.layout.pattern = ${logPattern}

# file appender configuration
appender.file.name = file
appender.file.type = RollingFile
appender.file.fileName = AppLogs/${filename}
appender.file.filePattern = AppLogs/${filename}%d{'_'yyyy-MM-dd}_%i.gz
appender.file.policies.type = Policies
appender.file.policies.size.type = SizeBasedTriggeringPolicy
appender.file.policies.size.size = 50MB
appender.file.policies.time.type = TimeBasedTriggeringPolicy
appender.file.policies.time.interval = 1
appender.rolling.policies.time.modulate = true
appender.file.strategy.type = DefaultRolloverStrategy
appender.file.strategy.max = 30
appender.file.strategy.action.type = Delete
appender.file.strategy.action.basePath = AppLogs/
appender.file.strategy.action.condition.type = IfLastModified
appender.file.strategy.action.condition.age = 30d

appender.file.layout.type = PatternLayout
appender.file.layout.pattern = ${logPattern}

rootLogger = info, file, console
#rootLogger.level = warn
#rootLogger.appenderRefs = file, console
#rootLogger.appenderRef.file.ref = file
#rootLogger.appenderRef.console.ref = console
