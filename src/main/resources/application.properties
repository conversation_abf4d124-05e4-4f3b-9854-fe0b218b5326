spring.application.name=tibco-external-integration-service
server.port=9009
spring.profiles.active=dev

#Health Management
management.endpoints.web.exposure.include=*

# Oracle DataSource Configuration
#spring.datasource.url=********************************************************
spring.datasource.url=jdbc:oracle:thin:@//${DB_URL}/${DB_NAME}
spring.datasource.username=${DB_USERNAME}
spring.datasource.password=${DB_PASSWORD}
spring.datasource.driver-class-name=oracle.jdbc.OracleDriver


# Request Time Monitoring
management.endpoint.prometheus.enabled=true
management.metrics.export.prometheus.enabled=true
management.tracing.enabled=true
management.tracing.sampling.probability=1.0
management.zipkin.tracing.endpoint=http://localhost:9411/api/v2/spans


# JPA Configuration
spring.jpa.database-platform=org.hibernate.dialect.OracleDialect
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.use_sql_comments=true

# Spring Doc
springdoc.api-docs.enabled=true
springdoc.swagger-ui.enabled=true
springdoc.api-docs.path=/swagger/tibco-external-integration-service/v3/api-docs
springdoc.swagger-ui.path=/swagger/tibco-external-integration-service/index.html

#
#soap.services.tibco_sms=com.paycraft.rta.abt.cam.external_integration.xml.tibco_sms.dto
#soap.services.tibco_email=com.paycraft.rta.abt.cam.external_integration.xml.tibco_email.dto
soap.services.epay_service=com.paycraft.rta.abt.cam.external_integration.domain.dto.autogenerated.epay_service

soap.services.epayTransaction=com.paycraft.rta.abt.cam.external_integration.domain.dto.autogenerated.epay_transaction_service



# 1. Feign logs all requests/responses
feign.client.config.default.loggerLevel=FULL

# 2. Enable DEBUG logging for Feign logger
logging.level.feign=DEBUG
# Optionally be more specific
logging.level.org.springframework.cloud.openfeign=DEBUG
logging.level.feign.Logger=DEBUG
logging.level.com.paycraft.rta.abt.cam.external_integration.feign=DEBUG

# Feign Retry configuration
feign.retry.period=1000
feign.retry.max-period=1000
feign.retry.max-attempts=3

#tomcat enhahncement

# Max concurrent request-processing threads
server.tomcat.max-threads=200
# Minimum idle threads ready to handle requests
server.tomcat.min-spare-threads=20
# Max queue length for incoming connections when all threads are busy
server.tomcat.accept-count=100

# (ms) Close idle connections after 20s
server.connection-timeout=20000
# Applies to embedded Tomcat
server.tomcat.connection-timeout=20000

#server.compression.enabled=true
#server.compression.min-response-size=1024
#server.compression.mime-types=application/json,application/xml,text/html,text/plain,text/css,text/javascript,application/javascript

#Avoid request failures due to headers being too large
server.max-http-header-size=16KB


# Max concurrent DB connections (tune based on DB limit & query concurrency)
spring.datasource.hikari.maximum-pool-size=100

# Keep some idle connections to avoid ramp-up delays
spring.datasource.hikari.minimum-idle=10

# Close idle connections after 5 minutes (good default)
spring.datasource.hikari.idle-timeout=300000
# Recycle connections after 30 minutes to avoid stale or firewall-killed connections
spring.datasource.hikari.max-lifetime=1800000
# Wait 30 sec for a connection before failing (useful under high load)
spring.datasource.hikari.connection-timeout=30000
# Validate connections quickly
spring.datasource.hikari.validation-timeout=5000



# Optional: Health Check
management.health.db.enabled=true
spring.datasource.hikari.health-check-properties.connectionTimeout=1000

#Lazy initialisation
#spring.main.lazy-initialization=true

feign.httpclient.enabled=true
feign.httpclient.max-connections=1000
feign.httpclient.max-connections-per-route=200

feign.client.config.default.connectTimeout=5000
feign.client.config.default.readTimeout=10000

#feign.compression.request.enabled=true
#feign.compression.response.enabled=true
