package com.paycraft.rta.abt.cam.external_integration.domain;

import lombok.Getter;
import org.springframework.http.HttpStatus;


@Getter
public enum ExternalFaultCodeEnum {
    PROFILE_NOT_EXIST("20013", "Profile Does Not exist", "1341"),
    NOT_ALLOWED_TO_QUERY("800014", "Not Allowed to Query Profiles Other Than Visitor or Resident", "1342"),
    INVALID_SEARCH_PARAM_COMBINATION("800007", "Invalid Search Param Combination", "1343"),
    PROCESSING_EXCEPTION("800009", "Exception in processing the request", "1344"),
    BACKEND_CONNECTION_ERROR("101503", "Error connecting to the back end", "1345"),
    INVALID_CREDENTIALS("900901", "Invalid credentials supplied", "1346"),
    NULL_OR_EMPTY_VALUES("800015", "NULL Or Empty values not allowed", "1347"),
    PROFILE_SEARCH_SERVICE_ERROR("500", "Exception while calling ms-profile-search service.", "1348"),
    PROFILE_INACTIVE("20014", "The person profile found is inactive.", "1349"),
    INVALID_REQUEST("20015", "Invalid request passed to the service", "1350"),
    INVALID_SEARCH_PARAM("800008", "Invalid Search Param", "1351"),

    OK("200", "Success. Response received", String.valueOf(HttpStatus.OK.value())),
    NO_CONTENT("204", "No Content", String.valueOf(HttpStatus.NO_CONTENT.value())),
    BAD_REQUEST("400", "Bad Request / Not all required parameters provided", String.valueOf(HttpStatus.BAD_REQUEST.value())),
    UNAUTHORIZED("401", "Unauthorized, Invalid or no credentials provided", String.valueOf(HttpStatus.UNAUTHORIZED.value())),
    FORBIDDEN("403", "Forbidden. Invalid credentials (Wrong username or password)", String.valueOf(HttpStatus.FORBIDDEN.value())),
    NOT_FOUND("404", "Not Found / URI is Invalid", String.valueOf(HttpStatus.NOT_FOUND.value())),
    METHOD_NOT_ALLOWED("405", "Method not allowed. Invalid method sent for API", String.valueOf(HttpStatus.METHOD_NOT_ALLOWED.value())),
    UNSUPPORTED_MEDIA_TYPE("415", "Unsupported Media Type", String.valueOf(HttpStatus.UNSUPPORTED_MEDIA_TYPE.value())),
    UNPROCESSABLE_ENTITY("422", "Unprocessable Entity", String.valueOf(HttpStatus.UNPROCESSABLE_ENTITY.value())),
    INTERNAL_SERVER_ERROR("500", "Internal Server Error", String.valueOf(HttpStatus.INTERNAL_SERVER_ERROR.value())),
    SERVICE_UNDER_MAINTENANCE("503", "Service Under maintenance", String.valueOf(HttpStatus.SERVICE_UNAVAILABLE.value()));

    private final String code;
    private final String message;
    private final String errorCode;

    ExternalFaultCodeEnum(String code, String message, String errorCode) {
        this.code = code;
        this.message = message;
        this.errorCode = errorCode;
    }

    public static ExternalFaultCodeEnum fromFaultCode(String faultCode) {
        for (ExternalFaultCodeEnum error : values()) {
            if (error.getCode().equals(faultCode)) {
                return error;
            }
        }
        return null;
    }
}