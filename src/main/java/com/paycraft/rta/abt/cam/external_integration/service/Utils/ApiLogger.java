package com.paycraft.rta.abt.cam.external_integration.service.Utils;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;

import static com.paycraft.rta.abt.cam.external_integration.domain.constants.LogsConstant.*;

@Component
public class ApiLogger {

    private static final Logger log = LogManager.getLogger(ApiLogger.class);

    public static void loggingExecutingTimings(String apiName, Instant apiHitTime,Instant apiResponseTime)
    {
        log.info("Api Name :{}",apiName);
        log.info("apiHitTime :{}",apiHitTime);
        log.info("apiResponseTime :{}",apiResponseTime);
        log.info("apiDuration :{}", Duration.between(apiHitTime,apiResponseTime));
        log.info("--------------------------------------------------------");
        log.info("externalServiceHitTime :{}",externalApiHitTime);
        log.info("externalServiceResponseTime :{}",externalApiResponseTime);
        log.info("serviceDuration :{}",Duration.between(externalApiHitTime,externalApiResponseTime));
    }
}
