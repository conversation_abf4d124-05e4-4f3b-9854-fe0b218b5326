package com.paycraft.rta.abt.cam.external_integration.feign;

import com.paycraft.rta.abt.cam.common.domain.external_dtos.khda.KhdaReponseDTO;
import com.paycraft.rta.abt.cam.external_integration.config.FeignProxyConfig;
import com.paycraft.rta.abt.cam.external_integration.domain.dto.KhdaResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "khdaClient", url = "${KHDA_SERVICE_URL}",configuration= FeignProxyConfig.class)
public interface KHDAClient {
    @GetMapping(value = "/getstudentdetails", consumes = "application/json")
    KhdaResponse getStudentDetails(@RequestParam("studentemiratesid") String studentEmiratesId);
}
