package com.paycraft.rta.abt.cam.external_integration;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;


@EnableFeignClients(basePackages = "com.paycraft.rta.abt.cam.external_integration.feign")
@SpringBootApplication
@ComponentScan(basePackages = {"com.paycraft.rta.abt.cam.external_integration", "com.paycraft.rta.abt.cam.common"})
@EnableTransactionManagement
@EnableJpaRepositories(basePackages = "com.paycraft.rta.abt.cam.common.repository")
@EntityScan(basePackages = "com.paycraft.rta.abt.cam.common.domain.entities")
public class ExternalIntegrationApplication {
    public static void main(String[] args) {
        System.setProperty("org.apache.commons.logging.Log", "org.apache.commons.logging.impl.SimpleLog");
        System.setProperty("org.apache.commons.logging.simplelog.log.org.apache.http.wire", "DEBUG");
        System.setProperty("org.apache.commons.logging.simplelog.log.org.apache.http.headers", "DEBUG");
        SpringApplication.run(ExternalIntegrationApplication.class, args);
    }

}
