package com.paycraft.rta.abt.cam.external_integration.mapper;

import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.user.GdrfaInfoRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.gdrfa.PersonInfoRequestDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

@Mapper(componentModel = "spring")
public interface GDRFAMapper {

    @Mappings({
            @Mapping(target = "genderId", source = "gender.gdrfaValue"),
            @Mapping(target = "dateOfBirth", ignore = true),
            @Mapping(target = "eidaNo", source = "emirateId"),
            @Mapping(target = "fetchPhoto", source = "fetchPhoto.gdrfaValue"),
            @Mapping(target = "passportNo",source = "source.passportNumber")
    })
    PersonInfoRequestDto mapToPersonInfoRequestDto(GdrfaInfoRequestDTO source);
}
