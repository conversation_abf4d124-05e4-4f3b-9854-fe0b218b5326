package com.paycraft.rta.abt.cam.external_integration.service.impl;

import com.paycraft.rta.abt.cam.external_integration.service.ExternalIntegrationService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

@Service
public class ExternalIntegrationServiceImpl implements ExternalIntegrationService {

    private static final Logger log = LogManager.getLogger(ExternalIntegrationServiceImpl.class);


}
