package com.paycraft.rta.abt.cam.external_integration.controller;

import com.paycraft.rta.abt.cam.common.domain.dtos.AppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.CreateAwbRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.CreateAwbResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.pcard.IsicDetailsDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.pcard.KhdaDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.pcard.SanadDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.user.GdrfaInfoRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.LanguageEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.MessageKeyEnum;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.emxIntegration.CancelShipmentResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.emxIntegration.CreateShipmentRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.emxIntegration.CreateShipmentResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.emxIntegration.TrackShipmentResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.gdrfa.PersonDetailsResponseDto;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.isic.*;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.khda.KhdaReponseDTO;
import com.paycraft.rta.abt.cam.external_integration.service.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.paycraft.rta.abt.cam.external_integration.domain.constants.LogsConstant.externalService;


@RestController
public class ExternalIntegrationController {

    private final Logger log = LogManager.getLogger(ExternalIntegrationController.class);

    @Autowired
    private GDRFIntegrationService gdrfIntegrationService;

    @Autowired
    private SANADIntegrationService sanadIntegrationService;

    @Autowired
    private KHDAIntegrationService khdaIntegrationService;

    @Autowired
    private ISICIntegrationService isicIntegrationService;

    @Autowired
    private EMXIntegrationService emxIntegrationService;

    @PostMapping("/gdrfa/profile")
    public AppResponseDTO<PersonDetailsResponseDto> getGdrfaProfile(@RequestHeader(name = "X-CLIENT-LANGUAGE", required = false,defaultValue = "EN") LanguageEnum clientLanguage ,@RequestBody GdrfaInfoRequestDTO request) {
        externalService="gdrfaService";
        return AppResponseDTO.of(MessageKeyEnum.SUCCESS, gdrfIntegrationService.getProfile(request, clientLanguage));
    }

    @GetMapping("/sanad/profile/{eidaNumber}")
    public AppResponseDTO<SanadDTO> getSanadProfile(@RequestHeader(name = "X-CLIENT-LANGUAGE", required = false,defaultValue = "EN") LanguageEnum clientLanguage,@PathVariable String eidaNumber) {
        externalService="sanadService";
        return AppResponseDTO.of(MessageKeyEnum.SUCCESS,sanadIntegrationService.getSanadCardDetails(eidaNumber,clientLanguage));
    }
    @GetMapping("/khda/inquiry/{studentsEmiratesId}")
    public AppResponseDTO<KhdaDTO> getStudentDetails(@RequestHeader(name = "X-CLIENT-LANGUAGE", required = false,defaultValue = "EN") LanguageEnum clientLanguage,@PathVariable String studentsEmiratesId){
        externalService="khdaService";
        return AppResponseDTO.of(MessageKeyEnum.SUCCESS,khdaIntegrationService.getStudentDetails(studentsEmiratesId, clientLanguage));
    }
    @PostMapping("/isic/create/card-holder")
    //public AppResponseDTO<CardHolderSetResponseDTO> createOrCardHolderSet(@RequestHeader(name = "X-CLIENT-LANGUAGE", required = false,defaultValue = "EN") LanguageEnum clientLanguage,@RequestBody CardHolderSetRequestDTO requestDTO){
    public AppResponseDTO<IsicDetailsDTO> createOrCardHolderSet(@RequestHeader(name = "X-CLIENT-LANGUAGE", required = false,defaultValue = "EN") LanguageEnum clientLanguage,@RequestBody CardHolderSetRequestDTO requestDTO){
        externalService="isicService";
        return AppResponseDTO.of(MessageKeyEnum.SUCCESS,isicIntegrationService.createOrUpdate(requestDTO, clientLanguage));
    }
    @PostMapping("/isic/create/card-report")
    public AppResponseDTO<CardReportResponseDTO> reportCard(@RequestHeader(name = "X-CLIENT-LANGUAGE", required = false ,defaultValue = "EN") LanguageEnum clientLanguage,@RequestBody CardReportRequestDTO requestDTO){
        externalService="isicService";
        return AppResponseDTO.of(MessageKeyEnum.SUCCESS,isicIntegrationService.cardReport(requestDTO,clientLanguage));
    }
    @PostMapping("/isic/change/cardStatus")
    public AppResponseDTO<CardReportResponseDTO> changeCardStatus(@RequestHeader(name = "X-CLIENT-LANGUAGE", required = false,defaultValue = "EN") LanguageEnum clientLanguage,@RequestBody UpdateCardStatusAndValidityDTO requestDTO){
        externalService="isicService";
        return AppResponseDTO.of(MessageKeyEnum.SUCCESS,isicIntegrationService.changeCardStatus(requestDTO,clientLanguage));
    }
    @PostMapping("/isic/change/cardValidity")
    public AppResponseDTO<CardReportResponseDTO> changeCardValidity(@RequestHeader(name = "X-CLIENT-LANGUAGE", required = false,defaultValue = "EN") LanguageEnum clientLanguage,@RequestBody UpdateCardStatusAndValidityDTO requestDTO){
        externalService="isicService";
        return AppResponseDTO.of(MessageKeyEnum.SUCCESS,isicIntegrationService.changeCardValidity(requestDTO , clientLanguage));
    }
    @PostMapping("/emxintegration/shipments/create")
    public AppResponseDTO<CreateAwbResponseDTO> createShipment(@RequestHeader(name = "X-CLIENT-LANGUAGE", required = false ,defaultValue = "EN") LanguageEnum clientLanguage,@RequestBody CreateAwbRequestDTO request) {
        externalService="emxIntegrationService";
        return AppResponseDTO.of(MessageKeyEnum.SUCCESS, emxIntegrationService.createShipment(request, clientLanguage));
    }

    @GetMapping("/emxintegration/shipments/track")
    public AppResponseDTO<List<TrackShipmentResponseDTO>> trackShipment(@RequestHeader(name = "X-CLIENT-LANGUAGE", required = false,defaultValue = "EN") LanguageEnum clientLanguage,@RequestParam String awbNumber) {
        externalService="emxIntegrationService";
        return AppResponseDTO.of(MessageKeyEnum.SUCCESS, emxIntegrationService.trackShipment(awbNumber ,clientLanguage));
    }

    @PostMapping("/emxintegration/shipments/cancel")
    public AppResponseDTO<CancelShipmentResponseDTO> cancelShipment(@RequestHeader(name = "X-CLIENT-LANGUAGE", required = false,defaultValue = "EN") LanguageEnum clientLanguage,@RequestParam String awbNumber) {
        externalService="emxIntegrationService";
        return AppResponseDTO.of(MessageKeyEnum.SUCCESS, emxIntegrationService.cancelShipment(awbNumber ,clientLanguage));
    }
}
