package com.paycraft.rta.abt.cam.external_integration.service;

import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.user.GdrfaInfoRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.LanguageEnum;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.gdrfa.PersonDetailsResponseDto;

public interface GDRFIntegrationService {
    PersonDetailsResponseDto getProfile(GdrfaInfoRequestDTO request , LanguageEnum clientLanauage);
}
