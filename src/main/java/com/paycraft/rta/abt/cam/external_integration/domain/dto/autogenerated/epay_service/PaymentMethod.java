//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.0 
// See https://eclipse-ee4j.github.io/jaxb-ri 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2025.08.22 at 11:46:20 AM IST 
//


package com.paycraft.rta.abt.cam.external_integration.domain.dto.autogenerated.epay_service;

import jakarta.xml.bind.annotation.XmlEnum;
import jakarta.xml.bind.annotation.XmlEnumValue;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for PaymentMethod.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <pre>
 * &lt;simpleType name="PaymentMethod"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="Not Selected"/&gt;
 *     &lt;enumeration value="Credit Card"/&gt;
 *     &lt;enumeration value="Edirham"/&gt;
 *     &lt;enumeration value="Direct Debit"/&gt;
 *     &lt;enumeration value="EdirhamG2"/&gt;
 *     &lt;enumeration value="OneClick Pay"/&gt;
 *     &lt;enumeration value="Wallet"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "PaymentMethod")
@XmlEnum
public enum PaymentMethod {

    @XmlEnumValue("Not Selected")
    NOT_SELECTED("Not Selected"),
    @XmlEnumValue("Credit Card")
    CREDIT_CARD("Credit Card"),
    @XmlEnumValue("Edirham")
    EDIRHAM("Edirham"),
    @XmlEnumValue("Direct Debit")
    DIRECT_DEBIT("Direct Debit"),
    @XmlEnumValue("EdirhamG2")
    EDIRHAM_G_2("EdirhamG2"),
    @XmlEnumValue("OneClick Pay")
    ONE_CLICK_PAY("OneClick Pay"),
    @XmlEnumValue("Wallet")
    WALLET("Wallet");
    private final String value;

    PaymentMethod(String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    public static PaymentMethod fromValue(String v) {
        for (PaymentMethod c: PaymentMethod.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

}
