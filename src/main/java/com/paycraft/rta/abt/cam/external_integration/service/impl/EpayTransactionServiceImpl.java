package com.paycraft.rta.abt.cam.external_integration.service.impl;

import com.paycraft.rta.abt.cam.common.domain.dtos.ValidationResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.ErrorCodesEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.LanguageEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.MessageKeyEnum;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.ePayTransactions.EPayTransactionSearchResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.ePayTransactions.EpayStatusServiceRequestDTO;
import com.paycraft.rta.abt.cam.common.exception.CbtIntegrationException;
import com.paycraft.rta.abt.cam.common.repository.CamMstTableDetailsRepository;
import com.paycraft.rta.abt.cam.external_integration.config.SoapClientRegistry;
import com.paycraft.rta.abt.cam.external_integration.domain.dto.autogenerated.epay_transaction_service.*;
import com.paycraft.rta.abt.cam.external_integration.mapper.EPayTransactionMapper;
import com.paycraft.rta.abt.cam.external_integration.mapper.TransactionMapper;
import com.paycraft.rta.abt.cam.external_integration.service.EpayTransactionService;
import com.paycraft.rta.abt.cam.external_integration.service.Utils.FaultParserUtil;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.ws.client.WebServiceClientException;
import org.springframework.ws.client.core.WebServiceTemplate;
import org.springframework.ws.soap.client.SoapFaultClientException;
import org.springframework.ws.soap.client.core.SoapActionCallback;

import java.lang.reflect.Method;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.paycraft.rta.abt.cam.external_integration.domain.constants.LogsConstant.externalApiHitTime;
import static com.paycraft.rta.abt.cam.external_integration.domain.constants.LogsConstant.externalApiResponseTime;

@Service
public class EpayTransactionServiceImpl implements EpayTransactionService {

    private static final Logger log = LogManager.getLogger(EpayTransactionServiceImpl.class);


    private final WebServiceTemplate template;
    @Value("${E_PAY_STATUS_URL}")
    private String SERVICE_URL;

    public EpayTransactionServiceImpl(SoapClientRegistry soapClientRegistry) {
        this.template = soapClientRegistry.getTemplate("epayTransaction");
    }


    @Autowired
    EPayTransactionMapper ePayTransactionMapper;

    @Autowired
    CamMstTableDetailsRepository camMstTableDetailsRepository;

    @Override
    public EPayTransactionSearchResponseDTO searchEpayTransaction(EpayStatusServiceRequestDTO requestDTO,  LanguageEnum clientLanauage) {
        log.info("calling external service :{}","epayTransactionSearch");
        log.info("Requestbody :{}",requestDTO);
        return callSoapService(requestDTO,EpayTransactionSearch.class,"ePayTransactionSearch", EpayTransactionSearchResponse.class);
    }
    @Override
    public EPayTransactionSearchResponseDTO getTransactionStatus(EpayStatusServiceRequestDTO requestDTO,  LanguageEnum clientLanauage) {
        log.info("calling external service :{}","getTransactionStatus");
        log.info("Requestbody :{}",requestDTO);
        return callSoapService(requestDTO,GetTransactionStatus.class,"getTransactionStatus", GetTransactionStatusResponse.class);

    }
    @Override
    public EPayTransactionSearchResponseDTO getDegTransactionStatus(EpayStatusServiceRequestDTO requestDTO , LanguageEnum clientLanauage) {
        log.info("calling external service :{}","getDegTransactionStatus");
        log.info("Requestbody :{}",requestDTO);
        return callSoapService(requestDTO,GetTransactionStatus.class,"getDEGTransactionStatus", GetDEGTransactionStatusResponse.class);
    }

    private  <res,req> EPayTransactionSearchResponseDTO callSoapService(EpayStatusServiceRequestDTO requestDTO,Class<req> requestClass,String soapAction,Class <res> responseClass) {

         req jaxRequest = requestClass.equals(EpayTransactionSearch.class)? (req) ePayTransactionMapper.toEpayTransactionSearch(requestDTO)
                 :(requestClass.equals(GetTransactionStatus.class)? (req) ePayTransactionMapper.toTransactionStatus(requestDTO) : (req) ePayTransactionMapper.toDegTransactionStatus(requestDTO));
         log.info("jaxRequest :{}",jaxRequest);
         SoapActionCallback soapActionCallback= new SoapActionCallback(soapAction);
         log.info("soapAction :{} , soapActionCallBack :{}",soapAction,soapActionCallback);
        res jaxResponse=null;
        EPayTransactionSearchResponseDTO searchResponseDTO=null;
        try {
            externalApiHitTime = Instant.now();
            jaxResponse = (res) template.marshalSendAndReceive(SERVICE_URL, jaxRequest, soapActionCallback);
            externalApiResponseTime = Instant.now();
            log.info("jaxRespone :{}",jaxResponse);
            Method getResultMethod = responseClass.getMethod("getResult");
            String responseXml = (String) getResultMethod.invoke(jaxResponse);
            searchResponseDTO=TransactionMapper.parse(responseXml);
        } catch (SoapFaultClientException ex) {
                List<ValidationResponseDTO> violations = new ArrayList<>();
                String referenceId = null;

                Optional<Fault> faultInfoOpt = Optional.ofNullable(FaultParserUtil.parseFault(ex,Fault.class));

                if (faultInfoOpt.isPresent()) {
                    Fault faultInfo = faultInfoOpt.get();
                    referenceId = faultInfo.getFaultCode();

                    // Since faultDetail is just a string, you might want to parse it if it contains more info
                    // For now, add faultString and faultDetail as violations
                    if (faultInfo.getFaultString() != null) {
                        violations.add(ValidationResponseDTO.of(
                                ErrorCodesEnum.RESOURCE_NOT_FOUND.getErrorCode(),
                                faultInfo.getFaultString()
                        ));
                    }
                    if (faultInfo.getFaultDetail() != null && !faultInfo.getFaultDetail().isBlank()) {
                        violations.add(ValidationResponseDTO.of(
                                ErrorCodesEnum.RESOURCE_NOT_FOUND.getErrorCode(),
                                "Detail: " + faultInfo.getFaultDetail()
                        ));
                    }
                }

                // Fallback if violations list is empty
                if (violations.isEmpty()) {
                    violations.add(ValidationResponseDTO.of(
                            ErrorCodesEnum.RESOURCE_NOT_FOUND.getErrorCode(),
                            ErrorCodesEnum.RESOURCE_NOT_FOUND.getMessage()
                    ));
                }

                throw new CbtIntegrationException(referenceId, violations, MessageKeyEnum.VALIDATION_FAILED.getCode(), MessageKeyEnum.VALIDATION_FAILED.getMessage());
            }
            catch (WebServiceClientException ex) {
                log.error("Web service error during ePay transaction search: {}", ex.getMessage(), ex);
            }
        catch (Exception e) {
            throw new RuntimeException(e);
        }
        return searchResponseDTO;
    }

}
