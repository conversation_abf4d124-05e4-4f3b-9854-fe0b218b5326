package com.paycraft.rta.abt.cam.external_integration.config;

import feign.Client;
import feign.RequestInterceptor;
import feign.auth.BasicAuthRequestInterceptor;
import lombok.Setter;

import org.apache.hc.client5.http.ssl.NoopHostnameVerifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import java.net.InetSocketAddress;
import java.net.Proxy;

import org.apache.hc.client5.http.ssl.TrustAllStrategy;
import org.apache.hc.core5.ssl.SSLContextBuilder;


@Configuration
public class FeignProxyConfig {

    @Value("${PROXY_HOST}")
    @Setter
    private String proxyHost;

    @Value("${PROXY_PORT}")
    @Setter
    private Integer proxyPort;

    @Value("${PROXY_USERNAME}")
    @Setter
    private String proxyUsername;

    @Value("${PROXY_PASSWORD}")
    @Setter
    private String proxyPassword;

    @Value("${FEIGN_BASIC_AUTH_USERNAME}")
    private String basicAuthUsername;

    @Value("${FEIGN_BASIC_AUTH_PASSWORD}")
    private String basicAuthPassword;


    @Bean
    public BasicAuthRequestInterceptor basicAuthRequestInterceptor() {
//        return new BasicAuthRequestInterceptor("ABTStgUser", "sdfsXASS131$@");
        return new BasicAuthRequestInterceptor(basicAuthUsername, basicAuthPassword);
    }


    @Bean
    public Client proxiedFeignClient() throws Exception {
        HostnameVerifier allowAllHosts = new NoopHostnameVerifier();

        Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyHost, proxyPort));

        return new Client.Proxied(null,null,
                proxy,
                proxyUsername,
                proxyPassword
        );
    }


}