package com.paycraft.rta.abt.cam.external_integration.config.headers;

import feign.RequestInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class GDRFAHeaderConfig {

    @Bean
    public RequestInterceptor customHeaderInterceptor() {
        return requestTemplate -> {
            // Set custom headers
            requestTemplate.header("Clientid", "10002");
            requestTemplate.header("Clientdept", "GDRFA-RTA");

            // Set content type only if not already set by encoder
            if (!requestTemplate.headers().containsKey("Content-Type")) {
                requestTemplate.header("Content-Type", "application/json");
            }
        };
    }
}
