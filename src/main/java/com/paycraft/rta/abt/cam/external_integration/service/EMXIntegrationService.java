package com.paycraft.rta.abt.cam.external_integration.service;

import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.CreateAwbRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.CreateAwbResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.LanguageEnum;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.emxIntegration.CancelShipmentResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.emxIntegration.CreateShipmentRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.emxIntegration.CreateShipmentResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.emxIntegration.TrackShipmentResponseDTO;

import java.util.List;

public interface EMXIntegrationService {

    CreateAwbResponseDTO createShipment(CreateAwbRequestDTO request, LanguageEnum clientLanauage);

    List<TrackShipmentResponseDTO> trackShipment(String awbNumber, LanguageEnum clientLanauage);

    CancelShipmentResponseDTO cancelShipment(String awbNumber, LanguageEnum clientLanauage);

}
