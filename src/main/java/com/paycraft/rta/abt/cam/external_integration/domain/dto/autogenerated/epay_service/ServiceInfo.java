//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.0 
// See https://eclipse-ee4j.github.io/jaxb-ri 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2025.08.22 at 11:46:20 AM IST 
//


package com.paycraft.rta.abt.cam.external_integration.domain.dto.autogenerated.epay_service;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for ServiceInfo complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ServiceInfo"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="serviceNameEn" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="serviceNameAr" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="serviceId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="gessServiceId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="beneficiaryInfos" type="{http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/Schema.xsd}BeneficiaryInfos"/&gt;
 *         &lt;element name="additionalParams" type="{http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/CommonTypesSchema.xsd}Map"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ServiceInfo", propOrder = {
    "serviceNameEn",
    "serviceNameAr",
    "serviceId",
    "gessServiceId",
    "beneficiaryInfos",
    "additionalParams"
})
public class ServiceInfo {

    @XmlElement(required = true)
    protected String serviceNameEn;
    @XmlElement(required = true)
    protected String serviceNameAr;
    @XmlElement(required = true)
    protected String serviceId;
    @XmlElement(required = true)
    protected String gessServiceId;
    @XmlElement(required = true)
    protected BeneficiaryInfos beneficiaryInfos;
    @XmlElement(required = true)
    protected Map additionalParams;

    /**
     * Gets the value of the serviceNameEn property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getServiceNameEn() {
        return serviceNameEn;
    }

    /**
     * Sets the value of the serviceNameEn property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setServiceNameEn(String value) {
        this.serviceNameEn = value;
    }

    /**
     * Gets the value of the serviceNameAr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getServiceNameAr() {
        return serviceNameAr;
    }

    /**
     * Sets the value of the serviceNameAr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setServiceNameAr(String value) {
        this.serviceNameAr = value;
    }

    /**
     * Gets the value of the serviceId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getServiceId() {
        return serviceId;
    }

    /**
     * Sets the value of the serviceId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setServiceId(String value) {
        this.serviceId = value;
    }

    /**
     * Gets the value of the gessServiceId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGessServiceId() {
        return gessServiceId;
    }

    /**
     * Sets the value of the gessServiceId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGessServiceId(String value) {
        this.gessServiceId = value;
    }

    /**
     * Gets the value of the beneficiaryInfos property.
     * 
     * @return
     *     possible object is
     *     {@link BeneficiaryInfos }
     *     
     */
    public BeneficiaryInfos getBeneficiaryInfos() {
        return beneficiaryInfos;
    }

    /**
     * Sets the value of the beneficiaryInfos property.
     * 
     * @param value
     *     allowed object is
     *     {@link BeneficiaryInfos }
     *     
     */
    public void setBeneficiaryInfos(BeneficiaryInfos value) {
        this.beneficiaryInfos = value;
    }

    /**
     * Gets the value of the additionalParams property.
     * 
     * @return
     *     possible object is
     *     {@link Map }
     *     
     */
    public Map getAdditionalParams() {
        return additionalParams;
    }

    /**
     * Sets the value of the additionalParams property.
     * 
     * @param value
     *     allowed object is
     *     {@link Map }
     *     
     */
    public void setAdditionalParams(Map value) {
        this.additionalParams = value;
    }

}
