package com.paycraft.rta.abt.cam.external_integration.service.impl;

import com.paycraft.rta.abt.cam.common.domain.dtos.ValidationResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.pcard.SanadDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.ErrorCodesEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.LanguageEnum;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.sanad.ErrorDigitalSanadCard;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.sanad.SanadCardDto;
import com.paycraft.rta.abt.cam.common.exception.SanadIntegrationException;
import com.paycraft.rta.abt.cam.common.exception.ValidationException;
import com.paycraft.rta.abt.cam.common.utils.DateTimeUtils;
import com.paycraft.rta.abt.cam.common.utils.LocaleUtil;
import com.paycraft.rta.abt.cam.common.utils.MessageResolver;
import com.paycraft.rta.abt.cam.external_integration.feign.SANADClient;
import com.paycraft.rta.abt.cam.external_integration.mapper.SanadCardMapper;
import com.paycraft.rta.abt.cam.external_integration.service.SANADIntegrationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.Locale;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.paycraft.rta.abt.cam.external_integration.domain.constants.LogsConstant.externalApiHitTime;
import static com.paycraft.rta.abt.cam.external_integration.domain.constants.LogsConstant.externalApiResponseTime;

@Slf4j
@Service
public class SanadIntegrationServiceImpl implements SANADIntegrationService {

    @Autowired
    private SANADClient sanadClient;
    @Autowired
    private SanadCardMapper sanadCardMapper;
    @Autowired
    private MessageResolver messageResolver;

    @Override
    public SanadDTO getSanadCardDetails(String eidaNumber , LanguageEnum clientLanguage) {
        externalApiHitTime= Instant.now();
        SanadCardDto sanadCardDto = sanadClient.getSanadCardDetails(eidaNumber);
        externalApiResponseTime =Instant.now();
        validateConcessionExpiry(sanadCardDto.getExpiryDate(), clientLanguage);
        log.info("Sanad card details -  {}: ", sanadCardDto);
        return sanadCardMapper.toSanadDTO(sanadCardDto);
    }

    private List<ValidationResponseDTO> toValidationErrors(List<ErrorDigitalSanadCard> errorDigitalSanadCard, LanguageEnum clientLanguage) {
        return errorDigitalSanadCard.stream()
                .map(err -> ValidationResponseDTO.of(err.getErrorCode(), err.getErrorMessage()))
                .collect(Collectors.toList());
    }

    private void validateConcessionExpiry(String expiryDate , LanguageEnum language ) {
        Locale locale = LocaleUtil.fromLanguageEnum(language);
        LocalDate sanadExpiryDate = OffsetDateTime.parse(expiryDate).toLocalDate();
        LocalDate dateNow = DateTimeUtils.getCurrentUTCDate();
        if(sanadExpiryDate.isBefore(dateNow) || sanadExpiryDate.equals(dateNow)){
            throw new ValidationException(List.of(ValidationResponseDTO.of(ErrorCodesEnum.CONCESSION_EXPIRED.getErrorCode(),messageResolver.resolveMessage(ErrorCodesEnum.CONCESSION_EXPIRED.getErrorCode().toString(),locale))));
        }
        log.info("Sanad Concession passed.");
    }
}
