package com.paycraft.rta.abt.cam.external_integration.service;

import com.paycraft.rta.abt.cam.common.domain.enums.LanguageEnum;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.ePayTransactions.ConfirmDeliveryResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.ePayTransactions.ConfirmServiceDeliveryRequestDTO;
import org.apache.commons.codec.language.bm.Lang;

public interface EPayService {
    ConfirmDeliveryResponseDTO confirmServiceDelivery(ConfirmServiceDeliveryRequestDTO confirmServiceDeliveryRequestDTO,  LanguageEnum clientLanauage);

    public void  getTransactionToken();
    public void confirmServiceDeliveryMock();
    public void getResponseTokenDetails();
}
