package com.paycraft.rta.abt.cam.external_integration.controller;

import com.paycraft.rta.abt.cam.common.domain.enums.LanguageEnum;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.ePayTransactions.ConfirmDeliveryResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.ePayTransactions.ConfirmServiceDeliveryRequestDTO;
import com.paycraft.rta.abt.cam.external_integration.service.EPayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class EPayServiceController {
   @Autowired
    EPayService ePayService;

    @PostMapping("/epayService/confirmDelivery")
    ResponseEntity<ConfirmDeliveryResponseDTO> searchEpayTransaction(@RequestHeader(name = "X-CLIENT-LANGUAGE", required = false ,defaultValue = "EN") LanguageEnum clientLanguage , @RequestBody ConfirmServiceDeliveryRequestDTO confirmServiceDeliveryRequestDTO){
        return ResponseEntity.ok(ePayService.confirmServiceDelivery(confirmServiceDeliveryRequestDTO, clientLanguage));
    }
}
