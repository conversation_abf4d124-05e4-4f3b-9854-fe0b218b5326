package com.paycraft.rta.abt.cam.external_integration.controller;

import com.paycraft.rta.abt.cam.common.assembler.AppResponseDtoAssembler;
import com.paycraft.rta.abt.cam.common.domain.dtos.AppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.LanguageEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.MessageKeyEnum;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.ePayTransactions.EPayTransactionSearchResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.ePayTransactions.EpayStatusServiceRequestDTO;
import com.paycraft.rta.abt.cam.external_integration.service.EpayTransactionService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import static com.paycraft.rta.abt.cam.external_integration.domain.constants.EndpointConstants.*;

@RestController
public class EPayTransactionController {

    @Autowired
    EpayTransactionService epayTransactionService;
    @Autowired
    AppResponseDtoAssembler assembler;

    private final Logger log = LogManager.getLogger(EPayTransactionController.class);

    @PostMapping(EPAY_TRANSACTION_SEARCH)
    ResponseEntity<AppResponseDTO<EPayTransactionSearchResponseDTO>> searchEpayTransaction(@RequestHeader(name = "X-CLIENT-LANGUAGE", required = false,defaultValue = "EN") LanguageEnum clientLanguage,@RequestBody EpayStatusServiceRequestDTO epayTransactionSearch){
        log.info("Inside EPayTransaction Controller :  {}" , epayTransactionSearch);
        return ResponseEntity.ok(assembler.getResponse(epayTransactionService.searchEpayTransaction(epayTransactionSearch ,clientLanguage), MessageKeyEnum.SUCCESS));
    }
    @PostMapping(EPAY_DEG_STATUS)
    ResponseEntity<AppResponseDTO<EPayTransactionSearchResponseDTO>> getDegTransactionStatus(@RequestHeader(name = "X-CLIENT-LANGUAGE", required = false ,defaultValue = "EN") LanguageEnum clientLanguage,@RequestBody EpayStatusServiceRequestDTO getDegStatusRequestDTO){
        log.info("Inside Epay_DEG_TransactionStatus Controller :  {}" , getDegStatusRequestDTO);
        return ResponseEntity.ok(assembler.getResponse(epayTransactionService.getDegTransactionStatus(getDegStatusRequestDTO, clientLanguage),MessageKeyEnum.SUCCESS));
    }
    @PostMapping(EPAY_STATUS)
    ResponseEntity<AppResponseDTO<EPayTransactionSearchResponseDTO>> getTransactionStatus(@RequestHeader(name = "X-CLIENT-LANGUAGE", required = false ,defaultValue = "EN") LanguageEnum clientLanguage,@RequestBody EpayStatusServiceRequestDTO getStatusRequestDTO){
        log.info("Inside EpayTransactionStatus Controller :  {}" , getStatusRequestDTO);
        return ResponseEntity.ok(assembler.getResponse(epayTransactionService.getTransactionStatus(getStatusRequestDTO,clientLanguage),MessageKeyEnum.SUCCESS));
    }

}
