package com.paycraft.rta.abt.cam.external_integration.config;

import org.apache.hc.client5.http.auth.AuthScope;
import org.apache.hc.client5.http.auth.CredentialsStore;
import org.apache.hc.client5.http.auth.UsernamePasswordCredentials;
import org.apache.hc.client5.http.impl.auth.BasicCredentialsProvider;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClientBuilder;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManagerBuilder;
import org.apache.hc.client5.http.ssl.SSLConnectionSocketFactory;
import org.apache.hc.core5.http.HttpHost;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.*;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.security.cert.X509Certificate;
import java.util.Collections;
import java.util.stream.Collectors;

@Configuration
public class RestTemplateConfig {

    @Bean
    @Qualifier("restTemplateWithProxy")
    public RestTemplate restTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.setInterceptors(Collections.singletonList(new LoggingInterceptor()));
        restTemplate.setRequestFactory(new HttpComponentsClientHttpRequestFactory(customHttpClient()));
        return restTemplate;
    }

    @Bean
    @Qualifier("restTemplateWithoutProxy")
    public RestTemplate restTemplateWithoutProxy() {
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.setInterceptors(Collections.singletonList(new LoggingInterceptor()));
        return restTemplate;
    }

    private CloseableHttpClient customHttpClient() {
        try {
            // SSL Context setup
            SSLContext sslContext = SSLContext.getInstance("TLS");
            TrustManager[] trustAllCerts = new TrustManager[]{
                    new X509TrustManager() {
                        public X509Certificate[] getAcceptedIssuers() { return null; }
                        public void checkClientTrusted(X509Certificate[] certs, String authType) {}
                        public void checkServerTrusted(X509Certificate[] certs, String authType) {}
                    }
            };
            sslContext.init(null, trustAllCerts, new java.security.SecureRandom());

            // Proxy configuration
            HttpHost proxy = new HttpHost("*************", 31281);

            // Proxy credentials setup
            // Proxy credentials setup
            CredentialsStore credentialsProvider = new BasicCredentialsProvider();
            credentialsProvider.setCredentials(
                    new AuthScope(proxy.getHostName(), proxy.getPort()),
                    new UsernamePasswordCredentials("rtadev", "EhejvZpCoFh5g".toCharArray())
            );

            SSLConnectionSocketFactory sslSocketFactory = new SSLConnectionSocketFactory(
                    sslContext,
                    (hostname, session) -> true // Accept all hostnames
            );

            return HttpClientBuilder.create()
                    .setProxy(proxy)
                    .setDefaultCredentialsProvider(credentialsProvider)
                    .setConnectionManager(PoolingHttpClientConnectionManagerBuilder.create()
                            .setSSLSocketFactory(sslSocketFactory)
                            .build())
                    //.addRequestInterceptorFirst(new ContentLengthHeaderRemover())
                    .build();

        } catch (Exception e) {
            throw new RuntimeException("Failed to create HTTP client", e);
        }
    }

    static class LoggingInterceptor implements ClientHttpRequestInterceptor {
        private void logRequestDetails(HttpRequest request, byte[] body) {
            System.out.println("=== HTTP Request ===");
            System.out.println("URI        : " + request.getURI());
            System.out.println("Method     : " + request.getMethod());
            System.out.println("Headers    : " + request.getHeaders());
            System.out.println("Request Body: " + new String(body, StandardCharsets.UTF_8));
        }

        private void logResponseDetails(ClientHttpResponse response) throws IOException {
            System.out.println("=== HTTP Response ===");
            System.out.println("Status Code  : " + response.getStatusCode());
            System.out.println("Status Text  : " + response.getStatusText());
            System.out.println("Headers      : " + response.getHeaders());

            BufferedReader reader = new BufferedReader(
                    new InputStreamReader(response.getBody(), StandardCharsets.UTF_8));

            String responseBody = reader.lines().collect(Collectors.joining("\n"));
            System.out.println("Response Body: " + responseBody);
        }

        @Override
        public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution) throws IOException {

            logRequestDetails(request, body);
            ClientHttpResponse response = execution.execute(request, body);
            logResponseDetails(response);
            return response;
        }
    }

}
