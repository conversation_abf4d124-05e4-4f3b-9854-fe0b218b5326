package com.paycraft.rta.abt.cam.external_integration.service.impl;

import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.pcard.KhdaDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.LanguageEnum;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.khda.KhdaReponseDTO;
import com.paycraft.rta.abt.cam.external_integration.domain.dto.KhdaResponse;
import com.paycraft.rta.abt.cam.external_integration.feign.KHDAClient;
import com.paycraft.rta.abt.cam.external_integration.mapper.KhdaMapper;
import com.paycraft.rta.abt.cam.external_integration.service.KHDAIntegrationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.Optional;

import static com.paycraft.rta.abt.cam.external_integration.domain.constants.LogsConstant.externalApiHitTime;
import static com.paycraft.rta.abt.cam.external_integration.domain.constants.LogsConstant.externalApiResponseTime;

@Service
public class KhdaIntegrationServiceImpl implements KHDAIntegrationService {

    @Autowired
    private KHDAClient khdaClient;

    @Autowired
    private KhdaMapper mapper;

    @Override
    public KhdaDTO getStudentDetails(String studentEmiratesId , LanguageEnum clientLanguage) {
        externalApiHitTime= Instant.now();
        KhdaResponse khdaResponse=khdaClient.getStudentDetails(studentEmiratesId);
        externalApiResponseTime =Instant.now();
        return Optional.ofNullable(khdaResponse)
                .map(KhdaResponse::getResults)
                .filter(results -> !results.isEmpty())
                .map(results -> results.get(0))
                .map(mapper::toKhdaDTO)
                .orElse(null);
    }
}
