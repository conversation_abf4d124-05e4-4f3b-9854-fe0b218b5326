package com.paycraft.rta.abt.cam.external_integration.service.aop;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paycraft.rta.abt.cam.external_integration.service.Utils.ApiLogger;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.stereotype.Component;

import java.time.Instant;

@Aspect
@Component
public class ApiLoggingAspect {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final Logger log = LogManager.getLogger(ApiLoggingAspect.class);
    @Around("execution(com.paycraft.rta.abt.cam.external_integration)")
    public Object logExecutionTime(ProceedingJoinPoint joinPoint) throws Throwable {
        Instant start = Instant.now();

        Object result = null;

        Instant end = Instant.now();

        String methodName = joinPoint.getSignature().toShortString();

        ApiLogger.loggingExecutingTimings(methodName, start, end);
        try {
            result = joinPoint.proceed();
            return result;
        } finally {
            try {
                log.info(" API RESPONSE: {}() result={} ",
                        methodName,objectMapper.writeValueAsString(result));
            } catch (Exception e) {
                log.info(" API RESPONSE: {}() result=[unserializable] duration={}ms",
                        methodName);
            }
        }
    }
}
