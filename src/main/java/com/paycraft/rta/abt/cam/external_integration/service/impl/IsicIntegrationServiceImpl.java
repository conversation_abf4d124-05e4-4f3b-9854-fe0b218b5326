package com.paycraft.rta.abt.cam.external_integration.service.impl;

import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.pcard.IsicDetailsDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.LanguageEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.YesOrNoEnum;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.isic.*;
import com.paycraft.rta.abt.cam.external_integration.feign.ISICClient;
import com.paycraft.rta.abt.cam.external_integration.service.ISICIntegrationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.Optional;

import static com.paycraft.rta.abt.cam.external_integration.domain.constants.LogsConstant.externalApiHitTime;
import static com.paycraft.rta.abt.cam.external_integration.domain.constants.LogsConstant.externalApiResponseTime;

@Service
public class IsicIntegrationServiceImpl implements ISICIntegrationService {

    @Autowired
    ISICClient isicClient;

    @Override
    public IsicDetailsDTO createOrUpdate(CardHolderSetRequestDTO requestDTO, LanguageEnum clientLanguage) {
        externalApiHitTime= Instant.now();
        CardHolderSetResponseDTO cardHolderSetResponseDTO = isicClient.createOrUpdate(requestDTO);
        externalApiResponseTime =Instant.now();
        IsicDetailsDTO isicDetailsDTO = new IsicDetailsDTO();
        Optional<Integer> cardholderIdOpt = Optional.ofNullable(cardHolderSetResponseDTO)
                .map(CardHolderSetResponseDTO::getData)
                .filter(list -> !list.isEmpty())
                .map(list -> list.get(0))
                .map(CardHolderDTO::getCardholderId);

        // Set cardholderId if present
        cardholderIdOpt.ifPresent(isicDetailsDTO::setIsicCardHolderId);

        // Set checkPassed based on whether cardholderId was found
        isicDetailsDTO.setIsicCheckPassed(
                cardholderIdOpt.isPresent() ? YesOrNoEnum.YES : YesOrNoEnum.NO
        );
        return isicDetailsDTO;
    }
    @Override
    public CardReportResponseDTO cardReport(CardReportRequestDTO requestDTO , LanguageEnum clientLanguage){
        externalApiHitTime =Instant.now();
        CardReportResponseDTO reportResponseDTO=isicClient.reportCard(requestDTO);
        externalApiResponseTime=Instant.now();
        return  reportResponseDTO;
    }

    @Override
    public CardReportResponseDTO changeCardStatus(UpdateCardStatusAndValidityDTO requestDTO , LanguageEnum clientLanguage) {
        externalApiHitTime =Instant.now();
        CardReportResponseDTO reportResponseDTO=isicClient.changeCardStatus(requestDTO);
        externalApiResponseTime =Instant.now();
        return reportResponseDTO;
    }

    @Override
    public CardReportResponseDTO changeCardValidity(UpdateCardStatusAndValidityDTO requestDTO, LanguageEnum clientLanguage) {
        externalApiHitTime =Instant.now();
        CardReportResponseDTO reportResponseDTO=isicClient.changeCardValidity(requestDTO);
        externalApiResponseTime =Instant.now();
        return reportResponseDTO;
    }

}
