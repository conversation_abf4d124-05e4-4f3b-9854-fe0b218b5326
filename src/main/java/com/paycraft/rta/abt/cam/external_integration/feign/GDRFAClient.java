package com.paycraft.rta.abt.cam.external_integration.feign;


import com.paycraft.rta.abt.cam.common.domain.external_dtos.gdrfa.PersonDetailsResponseDto;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.gdrfa.PersonInfoRequestDto;
import com.paycraft.rta.abt.cam.external_integration.config.FeignProxyConfig;
import com.paycraft.rta.abt.cam.external_integration.config.headers.GDRFAHeaderConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

@FeignClient(name = "gdrfaClient", url = "${GDRFA_SERVICE_URL}", configuration= {FeignProxyConfig.class, GDRFAHeaderConfig.class})
public interface GDRFAClient {

    @PostMapping(value = "/EIP/secure/gdrfa/1.0.0/profile", consumes = "application/json")
    PersonDetailsResponseDto getProfile(PersonInfoRequestDto request);
}
