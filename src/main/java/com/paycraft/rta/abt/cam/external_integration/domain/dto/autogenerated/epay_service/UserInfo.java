//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.0 
// See https://eclipse-ee4j.github.io/jaxb-ri 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2025.08.22 at 11:46:20 AM IST 
//


package com.paycraft.rta.abt.cam.external_integration.domain.dto.autogenerated.epay_service;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;


/**
 * User information from the Service Provider
 * 			
 * 
 * <p>Java class for UserInfo complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="UserInfo"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="isAuthenticated" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="userId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="userName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="fullNameEn" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="fullNameAr" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="mobileNo" type="{http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/CommonTypesSchema.xsd}MobileNo" minOccurs="0"/&gt;
 *         &lt;element name="email" type="{http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/CommonTypesSchema.xsd}Email" minOccurs="0"/&gt;
 *         &lt;element name="nationalityCode" type="{http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/CommonTypesSchema.xsd}CountryCode" minOccurs="0"/&gt;
 *         &lt;element name="emiratesId" type="{http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/CommonTypesSchema.xsd}EmiratesId" minOccurs="0"/&gt;
 *         &lt;element name="emirateCode" type="{http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/CommonTypesSchema.xsd}EmirateCode" minOccurs="0"/&gt;
 *         &lt;element name="poBox" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "UserInfo", propOrder = {
    "isAuthenticated",
    "userId",
    "userName",
    "fullNameEn",
    "fullNameAr",
    "mobileNo",
    "email",
    "nationalityCode",
    "emiratesId",
    "emirateCode",
    "poBox"
})
public class UserInfo {

    protected Boolean isAuthenticated;
    protected String userId;
    protected String userName;
    protected String fullNameEn;
    protected String fullNameAr;
    protected String mobileNo;
    protected String email;
    protected String nationalityCode;
    protected String emiratesId;
    @XmlSchemaType(name = "string")
    protected EmirateCode emirateCode;
    protected String poBox;

    /**
     * Gets the value of the isAuthenticated property.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isIsAuthenticated() {
        return isAuthenticated;
    }

    /**
     * Sets the value of the isAuthenticated property.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setIsAuthenticated(Boolean value) {
        this.isAuthenticated = value;
    }

    /**
     * Gets the value of the userId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUserId() {
        return userId;
    }

    /**
     * Sets the value of the userId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUserId(String value) {
        this.userId = value;
    }

    /**
     * Gets the value of the userName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUserName() {
        return userName;
    }

    /**
     * Sets the value of the userName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUserName(String value) {
        this.userName = value;
    }

    /**
     * Gets the value of the fullNameEn property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFullNameEn() {
        return fullNameEn;
    }

    /**
     * Sets the value of the fullNameEn property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFullNameEn(String value) {
        this.fullNameEn = value;
    }

    /**
     * Gets the value of the fullNameAr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFullNameAr() {
        return fullNameAr;
    }

    /**
     * Sets the value of the fullNameAr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFullNameAr(String value) {
        this.fullNameAr = value;
    }

    /**
     * Gets the value of the mobileNo property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMobileNo() {
        return mobileNo;
    }

    /**
     * Sets the value of the mobileNo property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMobileNo(String value) {
        this.mobileNo = value;
    }

    /**
     * Gets the value of the email property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEmail() {
        return email;
    }

    /**
     * Sets the value of the email property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEmail(String value) {
        this.email = value;
    }

    /**
     * Gets the value of the nationalityCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNationalityCode() {
        return nationalityCode;
    }

    /**
     * Sets the value of the nationalityCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNationalityCode(String value) {
        this.nationalityCode = value;
    }

    /**
     * Gets the value of the emiratesId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEmiratesId() {
        return emiratesId;
    }

    /**
     * Sets the value of the emiratesId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEmiratesId(String value) {
        this.emiratesId = value;
    }

    /**
     * Gets the value of the emirateCode property.
     * 
     * @return
     *     possible object is
     *     {@link EmirateCode }
     *     
     */
    public EmirateCode getEmirateCode() {
        return emirateCode;
    }

    /**
     * Sets the value of the emirateCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link EmirateCode }
     *     
     */
    public void setEmirateCode(EmirateCode value) {
        this.emirateCode = value;
    }

    /**
     * Gets the value of the poBox property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPoBox() {
        return poBox;
    }

    /**
     * Sets the value of the poBox property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPoBox(String value) {
        this.poBox = value;
    }

}
