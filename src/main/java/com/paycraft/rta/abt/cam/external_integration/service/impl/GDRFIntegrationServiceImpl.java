package com.paycraft.rta.abt.cam.external_integration.service.impl;

import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.user.GdrfaInfoRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.LanguageEnum;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.gdrfa.PersonDetailsResponseDto;
import com.paycraft.rta.abt.cam.external_integration.feign.GDRFAClient;
import com.paycraft.rta.abt.cam.external_integration.mapper.GDRFAMapper;
import com.paycraft.rta.abt.cam.external_integration.service.GDRFIntegrationService;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;

import static com.paycraft.rta.abt.cam.external_integration.domain.constants.LogsConstant.externalApiHitTime;
import static com.paycraft.rta.abt.cam.external_integration.domain.constants.LogsConstant.externalApiResponseTime;

@Service
public class GDRFIntegrationServiceImpl implements GDRFIntegrationService  {

    @Autowired
    private GDRFAClient gdrfaClient;

    @Autowired
    private GDRFAMapper gdrfaMapper;


    private final Logger log =LogManager.getLogger(GDRFIntegrationServiceImpl.class);

    @Override
    public PersonDetailsResponseDto getProfile(GdrfaInfoRequestDTO request , LanguageEnum clientLanguage) {
        externalApiHitTime = Instant.now();
        log.info("Request for GDRFA get Profile :{}", request);
        PersonDetailsResponseDto personDetailsResponseDto= gdrfaClient.getProfile(gdrfaMapper.mapToPersonInfoRequestDto(request));
        log.info("Response for GDRFA get Profile :{}", personDetailsResponseDto);
        externalApiResponseTime =Instant.now();
        return  personDetailsResponseDto;
    }
}
