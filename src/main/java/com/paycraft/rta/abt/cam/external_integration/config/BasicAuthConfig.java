package com.paycraft.rta.abt.cam.external_integration.config;


import feign.Client;
import feign.auth.BasicAuthRequestInterceptor;
import org.apache.hc.client5.http.ssl.NoopHostnameVerifier;
import org.apache.hc.client5.http.ssl.TrustAllStrategy;
import org.apache.hc.core5.ssl.SSLContextBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.net.ssl.SSLContext;

//@Configuration
//public class BasicAuthConfig {
//
//    @Bean
//    public BasicAuthRequestInterceptor basicAuthRequestInterceptor() {
//        return new BasicAuthRequestInterceptor("ABTStgUser", "sdfsXASS131$@");
//    }
//
//    @Bean
//    public Client feignClient() throws Exception {
//        SSLContext sslContext = SSLContextBuilder
//                .create()
//                .loadTrustMaterial(null, TrustAllStrategy.INSTANCE)
//                .build();
//
//        return new Client.Default(
//                sslContext.getSocketFactory(),
//                new NoopHostnameVerifier()
//        );
//    }
//
//}
