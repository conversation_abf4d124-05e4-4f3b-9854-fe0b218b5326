package com.paycraft.rta.abt.cam.external_integration.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.ws.client.WebServiceClientException;
import org.springframework.ws.client.support.interceptor.ClientInterceptor;
import org.springframework.ws.context.MessageContext;
import org.springframework.ws.soap.SoapMessage;

import org.springframework.ws.transport.WebServiceConnection;
import org.springframework.ws.transport.context.TransportContext;
import org.springframework.ws.transport.context.TransportContextHolder;

import javax.xml.transform.Source;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.stream.StreamResult;
import java.io.StringWriter;

@Component
@Slf4j
public class LoggingInterceptor implements ClientInterceptor {

    @Override
    public boolean handleRequest(MessageContext messageContext) {
        logSoapMessage("SOAP Request", (SoapMessage) messageContext.getRequest());
        return true;
    }

    @Override
    public boolean handleResponse(MessageContext messageContext) {
        logSoapMessage("SOAP Response", (SoapMessage) messageContext.getResponse());
        return true;
    }

    @Override
    public boolean handleFault(MessageContext messageContext) {
        logSoapMessage("SOAP Fault", (SoapMessage) messageContext.getResponse());
        return true;
    }

    @Override
    public void afterCompletion(MessageContext messageContext, Exception e) throws WebServiceClientException {
    }

    private void logSoapMessage(String label, SoapMessage message) {
        try {
            Source source = message.getEnvelope().getSource();
            Transformer transformer = TransformerFactory.newInstance().newTransformer();
            StringWriter stringWriter = new StringWriter();
            transformer.setOutputProperty(javax.xml.transform.OutputKeys.INDENT, "yes");
            transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "2");

            transformer.transform(source, new StreamResult(stringWriter));
            log.info("===== " + label + " =====");
            log.info(stringWriter.toString());
            // 2️⃣ Also log the URL, if possible
            TransportContext transportContext = TransportContextHolder.getTransportContext();
            if (transportContext != null) {
                WebServiceConnection connection = transportContext.getConnection();
                log.info("SOAP Request URL: " + connection.getUri());
            }
        } catch (Exception e) {
            System.err.println("Error logging " + label + ": " + e.getMessage());
        }
    }
}

