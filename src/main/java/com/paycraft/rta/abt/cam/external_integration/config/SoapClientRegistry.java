package com.paycraft.rta.abt.cam.external_integration.config;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
//import org.apache.http.HttpException;
//import org.apache.http.HttpHost;
//import org.apache.http.HttpRequest;
//import org.apache.http.HttpRequestInterceptor;
//import org.apache.http.auth.AuthScope;
//import org.apache.http.auth.UsernamePasswordCredentials;
//import org.apache.http.client.CredentialsProvider;
//import org.apache.http.impl.client.BasicCredentialsProvider;
//import org.apache.http.impl.client.CloseableHttpClient;
//import org.apache.http.impl.client.HttpClientBuilder;
//import org.apache.http.impl.client.ProxyAuthenticationStrategy;
//import org.apache.http.protocol.HttpContext;
import lombok.Setter;
import org.apache.hc.client5.http.auth.AuthScope;
import org.apache.hc.client5.http.auth.CredentialsStore;
import org.apache.hc.client5.http.auth.UsernamePasswordCredentials;
import org.apache.hc.client5.http.impl.auth.BasicCredentialsProvider;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClientBuilder;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManagerBuilder;
import org.apache.hc.client5.http.ssl.SSLConnectionSocketFactory;
import org.apache.hc.core5.http.*;
import org.apache.hc.core5.http.protocol.HttpContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.stereotype.Component;
import org.springframework.ws.client.core.WebServiceTemplate;
import org.springframework.ws.client.support.interceptor.ClientInterceptor;
import org.springframework.ws.soap.security.wss4j2.Wss4jSecurityInterceptor;
import org.springframework.ws.transport.http.HttpComponents5MessageSender;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.IOException;
import java.security.cert.X509Certificate;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

@Component
@RequiredArgsConstructor
public class SoapClientRegistry {

    private final SoapServiceProperties soapServiceProperties;

    private final Map<String, WebServiceTemplate> templateMap = new HashMap<>();

    private final LoggingInterceptor loggingInterceptor;

    private final Wss4jSecurityInterceptor securityInterceptor;

    @Value("${PROXY_HOST}")
    @Setter
    private String proxyHost;

    @Value("${PROXY_PORT}")
    @Setter
    private Integer proxyPort;

    @Value("${PROXY_USERNAME}")
    @Setter
    private String proxyUsername;

    @Value("${PROXY_PASSWORD}")
    @Setter
    private String proxyPassword;

    @Value("${FEIGN_BASIC_AUTH_USERNAME}")
    @Setter
    private String basicAuthUsername;

    @Value("${FEIGN_BASIC_AUTH_PASSWORD}")
    @Setter
    private  String basicAuthPassword;

    @PostConstruct
    public void init() {
        soapServiceProperties.getServices().forEach(this::register);
    }

    private void register(String key, String contextPath) {
        Jaxb2Marshaller marshaller = new Jaxb2Marshaller();
        marshaller.setContextPath(contextPath);
        try {
            marshaller.afterPropertiesSet();
        } catch (Exception e) {
            throw new RuntimeException("Failed to init marshaller for: " + key, e);
        }

        WebServiceTemplate template = new WebServiceTemplate();
        template.setMarshaller(marshaller);
        template.setUnmarshaller(marshaller);
        template.setInterceptors(new ClientInterceptor[]{ securityInterceptor,loggingInterceptor});

        HttpComponents5MessageSender messageSender = new HttpComponents5MessageSender();
        messageSender.setHttpClient(customHttpClient());

        template.setMessageSender(messageSender);

        templateMap.put(key, template);
    }

    public WebServiceTemplate getTemplate(String key) {
        WebServiceTemplate template = templateMap.get(key);
        if (template == null) {
            throw new IllegalArgumentException("No SOAP service found for key: " + key);
        }
        return template;
    }

    private CloseableHttpClient customHttpClient() {
        try {
            // Proxy configuration
            HttpHost proxy = new HttpHost(proxyHost, proxyPort);

            // Proxy credentials setup
            // Proxy credentials setup
            CredentialsStore credentialsProvider = new BasicCredentialsProvider();
            credentialsProvider.setCredentials(
                    new AuthScope(proxy.getHostName(), proxy.getPort()),
                    new UsernamePasswordCredentials(proxyUsername, proxyPassword.toCharArray())
            );

            return HttpClientBuilder.create()
                    .setProxy(proxy)
                    .setDefaultCredentialsProvider(credentialsProvider)
                    .setConnectionManager(PoolingHttpClientConnectionManagerBuilder.create()
                            .build())
                    .addRequestInterceptorFirst(new ContentLengthHeaderRemover())
//                    .addRequestInterceptorFirst((request, entity, context) -> {
//                        String auth = cbtProperties.getServer().getUsername()+":"+cbtProperties.getServer().getPassword();
//                        String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes());
//                        request.setHeader("Authorization", "Basic " + encodedAuth);
//                    })
                    .build();

        } catch (Exception e) {
            throw new RuntimeException("Failed to create HTTP client", e);
        }
    }

    private static class ContentLengthHeaderRemover implements HttpRequestInterceptor {
        @Override
        public void process(HttpRequest httpRequest, EntityDetails entityDetails, HttpContext httpContext)
                throws HttpException, IOException {
            httpRequest.removeHeaders("Content-Length");
        }
    }
}