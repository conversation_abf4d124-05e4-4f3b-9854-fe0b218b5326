//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.0 
// See https://eclipse-ee4j.github.io/jaxb-ri 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2025.08.22 at 11:46:20 AM IST 
//


package com.paycraft.rta.abt.cam.external_integration.domain.dto.autogenerated.epay_transaction_service;

import javax.xml.namespace.QName;
import jakarta.xml.bind.JAXBElement;
import jakarta.xml.bind.annotation.XmlElementDecl;
import jakarta.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.paycraft.rta.abt.cam.external_integration.xml.epay_transaction_service.dto package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _DegTrn_QNAME = new QName("http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/ePayStatusCommonSchema.xsd", "degTrn");
    private final static QName _PgCode_QNAME = new QName("http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/ePayStatusCommonSchema.xsd", "pgCode");
    private final static QName _PgTrn_QNAME = new QName("http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/ePayStatusCommonSchema.xsd", "pgTrn");
    private final static QName _Result_QNAME = new QName("http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/ePayStatusCommonSchema.xsd", "result");
    private final static QName _ServCode_QNAME = new QName("http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/ePayStatusCommonSchema.xsd", "servCode");
    private final static QName _SpTrn_QNAME = new QName("http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/ePayStatusCommonSchema.xsd", "spTrn");
    private final static QName _TimeOut_QNAME = new QName("http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/ePayStatusCommonSchema.xsd", "timeOut");
    private final static QName _FaultCode_QNAME = new QName("http://www.rta.ae/ActiveMatrix/ESB/ePayStatusService/Fault/XMLSchema", "FaultCode");
    private final static QName _FaultString_QNAME = new QName("http://www.rta.ae/ActiveMatrix/ESB/ePayStatusService/Fault/XMLSchema", "FaultString");
    private final static QName _FaultDetail_QNAME = new QName("http://www.rta.ae/ActiveMatrix/ESB/ePayStatusService/Fault/XMLSchema", "FaultDetail");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.paycraft.rta.abt.cam.external_integration.xml.epay_transaction_service.dto
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link Fault }
     * 
     */
    public Fault createFault() {
        return new Fault();
    }

    /**
     * Create an instance of {@link EpayTransactionSearch }
     * 
     */
    public EpayTransactionSearch createEpayTransactionSearch() {
        return new EpayTransactionSearch();
    }

    /**
     * Create an instance of {@link EpayTransactionSearchResponse }
     * 
     */
    public EpayTransactionSearchResponse createEpayTransactionSearchResponse() {
        return new EpayTransactionSearchResponse();
    }

    /**
     * Create an instance of {@link GetDEGTransactionStatus }
     * 
     */
    public GetDEGTransactionStatus createGetDEGTransactionStatus() {
        return new GetDEGTransactionStatus();
    }

    /**
     * Create an instance of {@link GetDEGTransactionStatusResponse }
     * 
     */
    public GetDEGTransactionStatusResponse createGetDEGTransactionStatusResponse() {
        return new GetDEGTransactionStatusResponse();
    }

    /**
     * Create an instance of {@link GetTransactionStatus }
     * 
     */
    public GetTransactionStatus createGetTransactionStatus() {
        return new GetTransactionStatus();
    }

    /**
     * Create an instance of {@link GetTransactionStatusResponse }
     * 
     */
    public GetTransactionStatusResponse createGetTransactionStatusResponse() {
        return new GetTransactionStatusResponse();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/ePayStatusCommonSchema.xsd", name = "degTrn")
    public JAXBElement<String> createDegTrn(String value) {
        return new JAXBElement<String>(_DegTrn_QNAME, String.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/ePayStatusCommonSchema.xsd", name = "pgCode")
    public JAXBElement<String> createPgCode(String value) {
        return new JAXBElement<String>(_PgCode_QNAME, String.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/ePayStatusCommonSchema.xsd", name = "pgTrn")
    public JAXBElement<String> createPgTrn(String value) {
        return new JAXBElement<String>(_PgTrn_QNAME, String.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/ePayStatusCommonSchema.xsd", name = "result")
    public JAXBElement<String> createResult(String value) {
        return new JAXBElement<String>(_Result_QNAME, String.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/ePayStatusCommonSchema.xsd", name = "servCode")
    public JAXBElement<String> createServCode(String value) {
        return new JAXBElement<String>(_ServCode_QNAME, String.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/ePayStatusCommonSchema.xsd", name = "spTrn")
    public JAXBElement<String> createSpTrn(String value) {
        return new JAXBElement<String>(_SpTrn_QNAME, String.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/ePayStatusCommonSchema.xsd", name = "timeOut")
    public JAXBElement<String> createTimeOut(String value) {
        return new JAXBElement<String>(_TimeOut_QNAME, String.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://www.rta.ae/ActiveMatrix/ESB/ePayStatusService/Fault/XMLSchema", name = "FaultCode")
    public JAXBElement<String> createFaultCode(String value) {
        return new JAXBElement<String>(_FaultCode_QNAME, String.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://www.rta.ae/ActiveMatrix/ESB/ePayStatusService/Fault/XMLSchema", name = "FaultString")
    public JAXBElement<String> createFaultString(String value) {
        return new JAXBElement<String>(_FaultString_QNAME, String.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://www.rta.ae/ActiveMatrix/ESB/ePayStatusService/Fault/XMLSchema", name = "FaultDetail")
    public JAXBElement<String> createFaultDetail(String value) {
        return new JAXBElement<String>(_FaultDetail_QNAME, String.class, null, value);
    }

}
