//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.0 
// See https://eclipse-ee4j.github.io/jaxb-ri 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2025.08.22 at 11:46:20 AM IST 
//


package com.paycraft.rta.abt.cam.external_integration.domain.dto.autogenerated.epay_service;

import javax.xml.datatype.XMLGregorianCalendar;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;

import java.lang.Error;


/**
 * <p>Java class for GetResponseTokenDetailsResponse complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="GetResponseTokenDetailsResponse"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="spCode" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="servCode" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="sptrn" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="degTrn" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="txnTimestamp" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="paymentMethod" type="{http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/Schema.xsd}PaymentMethod" minOccurs="0"/&gt;
 *         &lt;element name="message" type="{http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/CommonTypesSchema.xsd}Message" minOccurs="0"/&gt;
 *         &lt;element name="error" type="{http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/CommonTypesSchema.xsd}Error" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *       &lt;attribute name="valid" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "GetResponseTokenDetailsResponse", propOrder = {
    "spCode",
    "servCode",
    "sptrn",
    "degTrn",
    "txnTimestamp",
    "paymentMethod",
    "message",
    "error"
})
public class GetResponseTokenDetailsResponse {

    protected String spCode;
    protected String servCode;
    protected String sptrn;
    protected String degTrn;
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar txnTimestamp;
    @XmlSchemaType(name = "string")
    protected PaymentMethod paymentMethod;
    protected Message message;
    protected java.lang.Error error;
    @XmlAttribute(name = "valid")
    protected Boolean valid;

    /**
     * Gets the value of the spCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSpCode() {
        return spCode;
    }

    /**
     * Sets the value of the spCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSpCode(String value) {
        this.spCode = value;
    }

    /**
     * Gets the value of the servCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getServCode() {
        return servCode;
    }

    /**
     * Sets the value of the servCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setServCode(String value) {
        this.servCode = value;
    }

    /**
     * Gets the value of the sptrn property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSptrn() {
        return sptrn;
    }

    /**
     * Sets the value of the sptrn property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSptrn(String value) {
        this.sptrn = value;
    }

    /**
     * Gets the value of the degTrn property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDegTrn() {
        return degTrn;
    }

    /**
     * Sets the value of the degTrn property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDegTrn(String value) {
        this.degTrn = value;
    }

    /**
     * Gets the value of the txnTimestamp property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getTxnTimestamp() {
        return txnTimestamp;
    }

    /**
     * Sets the value of the txnTimestamp property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setTxnTimestamp(XMLGregorianCalendar value) {
        this.txnTimestamp = value;
    }

    /**
     * Gets the value of the paymentMethod property.
     * 
     * @return
     *     possible object is
     *     {@link PaymentMethod }
     *     
     */
    public PaymentMethod getPaymentMethod() {
        return paymentMethod;
    }

    /**
     * Sets the value of the paymentMethod property.
     * 
     * @param value
     *     allowed object is
     *     {@link PaymentMethod }
     *     
     */
    public void setPaymentMethod(PaymentMethod value) {
        this.paymentMethod = value;
    }

    /**
     * Gets the value of the message property.
     * 
     * @return
     *     possible object is
     *     {@link Message }
     *     
     */
    public Message getMessage() {
        return message;
    }

    /**
     * Sets the value of the message property.
     * 
     * @param value
     *     allowed object is
     *     {@link Message }
     *     
     */
    public void setMessage(Message value) {
        this.message = value;
    }

    /**
     * Gets the value of the error property.
     * 
     * @return
     *     possible object is
     *     {@link java.lang.Error }
     *     
     */
    public java.lang.Error getError() {
        return error;
    }

    /**
     * Sets the value of the error property.
     * 
     * @param value
     *     allowed object is
     *     {@link java.lang.Error }
     *     
     */
    public void setError(Error value) {
        this.error = value;
    }

    /**
     * Gets the value of the valid property.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isValid() {
        return valid;
    }

    /**
     * Sets the value of the valid property.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setValid(Boolean value) {
        this.valid = value;
    }

}
