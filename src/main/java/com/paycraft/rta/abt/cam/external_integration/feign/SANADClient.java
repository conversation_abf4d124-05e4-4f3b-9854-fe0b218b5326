package com.paycraft.rta.abt.cam.external_integration.feign;

import com.paycraft.rta.abt.cam.common.domain.external_dtos.sanad.SanadCardDto;
import com.paycraft.rta.abt.cam.external_integration.config.FeignProxyConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "sanadClient", url = "${SANAD_SERVICE_URL}", configuration= FeignProxyConfig.class)
public interface SANADClient {

    @GetMapping(value="/getsanadcarddetails/{eidaNumber}",consumes = "application/json")
    SanadCardDto getSanadCardDetails(@PathVariable("eidaNumber") String eidaNumber);
}
