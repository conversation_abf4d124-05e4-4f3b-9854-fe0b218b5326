//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.0 
// See https://eclipse-ee4j.github.io/jaxb-ri 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2025.08.22 at 11:46:20 AM IST 
//


package com.paycraft.rta.abt.cam.external_integration.domain.dto.autogenerated.epay_transaction_service;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="spCode" type="{http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/ePayStatusCommonSchema.xsd}spCpdeType"/&gt;
 *         &lt;element name="servCode" type="{http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/ePayStatusCommonSchema.xsd}servCodeType"/&gt;
 *         &lt;element name="spTrn" type="{http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/ePayStatusCommonSchema.xsd}spTrnType"/&gt;
 *         &lt;element name="degTrn" type="{http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/ePayStatusCommonSchema.xsd}degTrnType"/&gt;
 *         &lt;element name="pgCode" type="{http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/ePayStatusCommonSchema.xsd}pgCodeType"/&gt;
 *         &lt;element name="pgTrn" type="{http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/ePayStatusCommonSchema.xsd}pgTrnType"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "spCode",
    "servCode",
    "spTrn",
    "degTrn",
    "pgCode",
    "pgTrn"
})
@XmlRootElement(name = "epayTransactionSearch")
public class EpayTransactionSearch {

    @XmlElement(required = true)
    protected String spCode;
    @XmlElement(required = true)
    protected String servCode;
    @XmlElement(required = true)
    protected String spTrn;
    @XmlElement(required = true)
    protected String degTrn;
    @XmlElement(required = true)
    protected String pgCode;
    @XmlElement(required = true)
    protected String pgTrn;

    /**
     * Gets the value of the spCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSpCode() {
        return spCode;
    }

    /**
     * Sets the value of the spCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSpCode(String value) {
        this.spCode = value;
    }

    /**
     * Gets the value of the servCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getServCode() {
        return servCode;
    }

    /**
     * Sets the value of the servCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setServCode(String value) {
        this.servCode = value;
    }

    /**
     * Gets the value of the spTrn property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSpTrn() {
        return spTrn;
    }

    /**
     * Sets the value of the spTrn property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSpTrn(String value) {
        this.spTrn = value;
    }

    /**
     * Gets the value of the degTrn property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDegTrn() {
        return degTrn;
    }

    /**
     * Sets the value of the degTrn property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDegTrn(String value) {
        this.degTrn = value;
    }

    /**
     * Gets the value of the pgCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPgCode() {
        return pgCode;
    }

    /**
     * Sets the value of the pgCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPgCode(String value) {
        this.pgCode = value;
    }

    /**
     * Gets the value of the pgTrn property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPgTrn() {
        return pgTrn;
    }

    /**
     * Sets the value of the pgTrn property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPgTrn(String value) {
        this.pgTrn = value;
    }

}
