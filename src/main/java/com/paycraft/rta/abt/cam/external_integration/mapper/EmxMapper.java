package com.paycraft.rta.abt.cam.external_integration.mapper;

import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.CreateAwbRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.CreateAwbResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.YesOrNoEnum;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.emxIntegration.CreateShipmentRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.emxIntegration.CreateShipmentResponseDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface EmxMapper {

    @Mapping(target = "printType",expression = "java(request.getPrintType().getCbtType())")
    @Mapping(target = "productCode",expression = "java(request.getProductCode().getCbtType())")
    @Mapping(target = "serviceType",expression = "java(request.getServiceType().getCbtType())")
    @Mapping(target = "deliveryType",expression = "java(request.getDeliveryType().getCbtType())")
    @Mapping(target = "contentType",expression = "java(request.getContentType().getCbtType())")
    @Mapping(target = "sendMailToSender",expression = "java(yesOrNoToBoolean(request.getSendMailToSender()))")
    CreateShipmentRequestDTO toEmxRequest(CreateAwbRequestDTO request);

    default boolean yesOrNoToBoolean(YesOrNoEnum data){
        return YesOrNoEnum.YES==data?true:false;
    }

    CreateAwbResponseDTO toEmxAwbResponse(CreateShipmentResponseDTO emxResponse);
}
