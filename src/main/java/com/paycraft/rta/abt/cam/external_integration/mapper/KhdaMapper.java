package com.paycraft.rta.abt.cam.external_integration.mapper;

import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.pcard.KhdaDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.YesOrNoEnum;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.khda.KhdaReponseDTO;

import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface KhdaMapper {

    @Mappings({
            @Mapping(source = "academicYear", target = "academicYear"),
            @Mapping(source = "schoolNameEnglish", target = "schoolName"),
            @Mapping(source = "studentEmiratesId", target = "status", qualifiedByName = "mapStatus")
    })
    KhdaDTO toKhdaDTO(KhdaReponseDTO khdaReponseDTO);

    @Named("mapStatus")
    default YesOrNoEnum mapStatus(String studentEmiratesId) {
        return (studentEmiratesId != null && !studentEmiratesId.trim().isEmpty())
                ? YesOrNoEnum.YES
                : YesOrNoEnum.NO;
    }
}
