//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.0 
// See https://eclipse-ee4j.github.io/jaxb-ri 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2025.08.22 at 11:46:20 AM IST 
//


package com.paycraft.rta.abt.cam.external_integration.domain.dto.autogenerated.epay_service;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for GetResponseTokenDetails complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="GetResponseTokenDetails"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="responseToken" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="spCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="servCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "GetResponseTokenDetails", propOrder = {
    "responseToken",
    "spCode",
    "servCode"
})
public class GetResponseTokenDetails {

    @XmlElement(required = true)
    protected String responseToken;
    @XmlElement(required = true)
    protected String spCode;
    @XmlElement(required = true)
    protected String servCode;

    /**
     * Gets the value of the responseToken property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getResponseToken() {
        return responseToken;
    }

    /**
     * Sets the value of the responseToken property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setResponseToken(String value) {
        this.responseToken = value;
    }

    /**
     * Gets the value of the spCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSpCode() {
        return spCode;
    }

    /**
     * Sets the value of the spCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSpCode(String value) {
        this.spCode = value;
    }

    /**
     * Gets the value of the servCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getServCode() {
        return servCode;
    }

    /**
     * Sets the value of the servCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setServCode(String value) {
        this.servCode = value;
    }

}
