//package com.paycraft.rta.abt.cam.external_integration.service.impl;
//
//
//import com.paycraft.rta.abt.cam.external_integration.config.SoapClientRegistry;
//import com.paycraft.rta.abt.cam.external_integration.xml.tibco_email.dto.Response;
//import com.paycraft.rta.abt.cam.external_integration.xml.tibco_email.dto.SendEmailRequest;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//import org.springframework.ws.soap.client.core.SoapActionCallback;
//
//@Service
//public class EmailServiceImpl {
//
//    @Autowired
//    SoapClientRegistry soapClientRegistry;
//
//    public void sendEmail() {
//        // Create factory (generated by JAXB)
//        SendEmailRequest request = new SendEmailRequest();
//        request.setFrom("<EMAIL>");
//        request.getTo().add("<EMAIL>");
//        request.setSendIndividually(true);
//        request.setSubject("Test Mail");
//        request.setBodyText("Sample Mail");
//        request.setBodyContentType("HTML");
//
//
//        // Create JAXBElement wrapper
//        //JAXBElement<SMSServiceRequest> requestElement = factory.createSMSServiceRequest(request);
//
//        //WebServiceTemplate template = soapClientRegistry.getTemplate("tibco_email");
//        //template.setInterceptors(new ClientInterceptor[]{smsSecurityInterceptor,loggingInterceptor});
//
//        // Call service
//        Response response = (Response)
//                soapClientRegistry.getTemplate("tibco_email").marshalSendAndReceive("https://eipstg.rtatestdom.local:11121/eProxy/service/SendEmailService",
//                        request,
//                        new SoapActionCallback("SendEMAIL"));
//
//        System.out.println("Status: " + response.getStatus());
//    }
//}
