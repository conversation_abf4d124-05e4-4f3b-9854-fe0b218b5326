//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.0 
// See https://eclipse-ee4j.github.io/jaxb-ri 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2025.08.22 at 11:46:20 AM IST 
//


package com.paycraft.rta.abt.cam.external_integration.domain.dto.autogenerated.epay_service;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for BeneficiaryInfo complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="BeneficiaryInfo"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="accountId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="txnAmount" type="{http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/CommonTypesSchema.xsd}Amount" minOccurs="0"/&gt;
 *         &lt;element name="fullNameEn" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="fullNameAr" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="mobileNo" type="{http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/CommonTypesSchema.xsd}MobileNo" minOccurs="0"/&gt;
 *         &lt;element name="email" type="{http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/CommonTypesSchema.xsd}Email" minOccurs="0"/&gt;
 *         &lt;element name="emiratesId" type="{http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/CommonTypesSchema.xsd}EmiratesId" minOccurs="0"/&gt;
 *         &lt;element name="type" type="{http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/Schema.xsd}BeneficiaryType" minOccurs="0"/&gt;
 *         &lt;element name="companyInfo" type="{http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/Schema.xsd}CompanyInfo" minOccurs="0"/&gt;
 *         &lt;element name="additionalParams" type="{http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/CommonTypesSchema.xsd}Map" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "BeneficiaryInfo", propOrder = {
    "accountId",
    "txnAmount",
    "fullNameEn",
    "fullNameAr",
    "mobileNo",
    "email",
    "emiratesId",
    "type",
    "companyInfo",
    "additionalParams"
})
public class BeneficiaryInfo {

    protected String accountId;
    protected Amount txnAmount;
    protected String fullNameEn;
    protected String fullNameAr;
    protected String mobileNo;
    protected String email;
    protected String emiratesId;
    @XmlSchemaType(name = "string")
    protected BeneficiaryType type;
    protected CompanyInfo companyInfo;
    protected Map additionalParams;

    /**
     * Gets the value of the accountId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAccountId() {
        return accountId;
    }

    /**
     * Sets the value of the accountId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAccountId(String value) {
        this.accountId = value;
    }

    /**
     * Gets the value of the txnAmount property.
     * 
     * @return
     *     possible object is
     *     {@link Amount }
     *     
     */
    public Amount getTxnAmount() {
        return txnAmount;
    }

    /**
     * Sets the value of the txnAmount property.
     * 
     * @param value
     *     allowed object is
     *     {@link Amount }
     *     
     */
    public void setTxnAmount(Amount value) {
        this.txnAmount = value;
    }

    /**
     * Gets the value of the fullNameEn property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFullNameEn() {
        return fullNameEn;
    }

    /**
     * Sets the value of the fullNameEn property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFullNameEn(String value) {
        this.fullNameEn = value;
    }

    /**
     * Gets the value of the fullNameAr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFullNameAr() {
        return fullNameAr;
    }

    /**
     * Sets the value of the fullNameAr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFullNameAr(String value) {
        this.fullNameAr = value;
    }

    /**
     * Gets the value of the mobileNo property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMobileNo() {
        return mobileNo;
    }

    /**
     * Sets the value of the mobileNo property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMobileNo(String value) {
        this.mobileNo = value;
    }

    /**
     * Gets the value of the email property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEmail() {
        return email;
    }

    /**
     * Sets the value of the email property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEmail(String value) {
        this.email = value;
    }

    /**
     * Gets the value of the emiratesId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEmiratesId() {
        return emiratesId;
    }

    /**
     * Sets the value of the emiratesId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEmiratesId(String value) {
        this.emiratesId = value;
    }

    /**
     * Gets the value of the type property.
     * 
     * @return
     *     possible object is
     *     {@link BeneficiaryType }
     *     
     */
    public BeneficiaryType getType() {
        return type;
    }

    /**
     * Sets the value of the type property.
     * 
     * @param value
     *     allowed object is
     *     {@link BeneficiaryType }
     *     
     */
    public void setType(BeneficiaryType value) {
        this.type = value;
    }

    /**
     * Gets the value of the companyInfo property.
     * 
     * @return
     *     possible object is
     *     {@link CompanyInfo }
     *     
     */
    public CompanyInfo getCompanyInfo() {
        return companyInfo;
    }

    /**
     * Sets the value of the companyInfo property.
     * 
     * @param value
     *     allowed object is
     *     {@link CompanyInfo }
     *     
     */
    public void setCompanyInfo(CompanyInfo value) {
        this.companyInfo = value;
    }

    /**
     * Gets the value of the additionalParams property.
     * 
     * @return
     *     possible object is
     *     {@link Map }
     *     
     */
    public Map getAdditionalParams() {
        return additionalParams;
    }

    /**
     * Sets the value of the additionalParams property.
     * 
     * @param value
     *     allowed object is
     *     {@link Map }
     *     
     */
    public void setAdditionalParams(Map value) {
        this.additionalParams = value;
    }

}
