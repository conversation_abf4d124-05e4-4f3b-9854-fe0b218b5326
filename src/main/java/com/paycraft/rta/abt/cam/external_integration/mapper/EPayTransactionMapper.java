package com.paycraft.rta.abt.cam.external_integration.mapper;

import com.paycraft.rta.abt.cam.common.domain.external_dtos.ePayTransactions.ConfirmServiceDeliveryRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.ePayTransactions.EpayStatusServiceRequestDTO;

import com.paycraft.rta.abt.cam.external_integration.domain.dto.autogenerated.epay_service.ConfirmServiceDelivery;
import com.paycraft.rta.abt.cam.external_integration.domain.dto.autogenerated.epay_transaction_service.EpayTransactionSearch;
import com.paycraft.rta.abt.cam.external_integration.domain.dto.autogenerated.epay_transaction_service.GetDEGTransactionStatus;
import com.paycraft.rta.abt.cam.external_integration.domain.dto.autogenerated.epay_transaction_service.GetTransactionStatus;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface EPayTransactionMapper {
    EpayTransactionSearch toEpayTransactionSearch(EpayStatusServiceRequestDTO epayStatusServiceRequestDTO);

    ConfirmServiceDelivery toConfirmServiceDeliveryRequestDTO(ConfirmServiceDeliveryRequestDTO confirmServiceDeliveryRequestDTO);

    GetDEGTransactionStatus toDegTransactionStatus(EpayStatusServiceRequestDTO statusServiceRequestDTO);

    GetTransactionStatus toTransactionStatus(EpayStatusServiceRequestDTO statusServiceRequestDTO);

}
