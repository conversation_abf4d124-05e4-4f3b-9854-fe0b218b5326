package com.paycraft.rta.abt.cam.external_integration.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.ws.soap.security.wss4j2.Wss4jSecurityInterceptor;
import org.apache.wss4j.common.ext.WSSecurityException;

@Configuration
public class SecurityConfig {

    @Value("${FEIGN_BASIC_AUTH_USERNAME}")
    private String basicAuthUsername;

    @Value("${FEIGN_BASIC_AUTH_PASSWORD}")
    private String basicAuthPassword;

    @Bean
    public Wss4jSecurityInterceptor securityInterceptor() {
        Wss4jSecurityInterceptor interceptor = new Wss4jSecurityInterceptor();
        interceptor.setSecurementActions("UsernameToken");
        interceptor.setSecurementUsername(basicAuthUsername);
        interceptor.setSecurementPassword(basicAuthPassword);
        interceptor.setSecurementPasswordType("PasswordText");
        return interceptor;
    }
}