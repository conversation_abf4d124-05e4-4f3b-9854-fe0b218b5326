package com.paycraft.rta.abt.cam.external_integration.feign;

import com.paycraft.rta.abt.cam.common.domain.external_dtos.emxIntegration.CancelShipmentResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.emxIntegration.CreateShipmentRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.emxIntegration.CreateShipmentResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.emxIntegration.TrackShipmentResponseDTO;
import com.paycraft.rta.abt.cam.external_integration.config.FeignProxyConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "emxClient", url = "${EMX_SERVICE_URL}",configuration = FeignProxyConfig.class)
public interface EMXClient {

    @PostMapping(value = "/EMXIntegrationService/api/Shipments/create", consumes = "application/json")
    CreateShipmentResponseDTO createShipment(CreateShipmentRequestDTO requestDTO);

    @GetMapping(value = "/EMXIntegrationService/tracking/api/Tracking", consumes = "application/json")
    List<TrackShipmentResponseDTO> trackShipment(@RequestParam("awbNumber") String awbNumber);

    @PostMapping(value = "/EMXIntegrationService/api/Shipments/cancel", consumes = "application/json")
    CancelShipmentResponseDTO cancelShipment(@RequestParam("awb") String awbNumber);

}
