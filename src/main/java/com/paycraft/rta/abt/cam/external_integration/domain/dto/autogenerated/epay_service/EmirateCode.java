//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.0 
// See https://eclipse-ee4j.github.io/jaxb-ri 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2025.08.22 at 11:46:20 AM IST 
//


package com.paycraft.rta.abt.cam.external_integration.domain.dto.autogenerated.epay_service;

import jakarta.xml.bind.annotation.XmlEnum;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for EmirateCode.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <pre>
 * &lt;simpleType name="EmirateCode"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="AUH"/&gt;
 *     &lt;enumeration value="DXB"/&gt;
 *     &lt;enumeration value="SHJ"/&gt;
 *     &lt;enumeration value="AJM"/&gt;
 *     &lt;enumeration value="UAQ"/&gt;
 *     &lt;enumeration value="RAK"/&gt;
 *     &lt;enumeration value="FUJ"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "EmirateCode", namespace = "http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/CommonTypesSchema.xsd")
@XmlEnum
public enum EmirateCode {


    /**
     * Abu Dhabi
     * 
     */
    AUH,

    /**
     * Dubai
     * 
     */
    DXB,

    /**
     * Sharjah
     * 
     */
    SHJ,

    /**
     * Ajman
     * 
     */
    AJM,

    /**
     * Umm Al Quwain
     * 
     */
    UAQ,

    /**
     * Ras Al Khaimah
     * 
     */
    RAK,

    /**
     * Fujairah
     * 
     */
    FUJ;

    public String value() {
        return name();
    }

    public static EmirateCode fromValue(String v) {
        return valueOf(v);
    }

}
