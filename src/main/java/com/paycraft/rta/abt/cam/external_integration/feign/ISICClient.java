package com.paycraft.rta.abt.cam.external_integration.feign;

import com.paycraft.rta.abt.cam.common.domain.external_dtos.isic.*;
import com.paycraft.rta.abt.cam.external_integration.config.FeignProxyConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

@FeignClient(name = "isicClient", url = "${ISIC_SERVICE_URL}", configuration= FeignProxyConfig.class)
public interface ISICClient {

    @PostMapping(value = "/ISICIntegrationService/cardHoldersSet", consumes = "application/json")
    CardHolderSetResponseDTO createOrUpdate(CardHolderSetRequestDTO requestDTO);

    @PostMapping(value = "/ISICIntegrationService/cardsReport", consumes = "application/json")
    CardReportResponseDTO reportCard(CardReportRequestDTO requestDTO);

    @PostMapping(value = "/ISICIntegrationService/cardsChangeStatus", consumes = "application/json")
    CardReportResponseDTO changeCardStatus(UpdateCardStatusAndValidityDTO requestDTO);

    @PostMapping(value = "/ISICIntegrationService/cardsChangeValidity", consumes = "application/json")
    CardReportResponseDTO changeCardValidity(UpdateCardStatusAndValidityDTO requestDTO);
}
