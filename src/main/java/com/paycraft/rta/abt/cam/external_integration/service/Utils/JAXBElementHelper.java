package com.paycraft.rta.abt.cam.external_integration.service.Utils;

import com.paycraft.rta.abt.cam.external_integration.domain.dto.autogenerated.epay_transaction_service.EpayTransactionSearch;
import jakarta.xml.bind.JAXBElement;

import javax.xml.namespace.QName;

public class JAXBElementHelper {

    private static final QName _EpayTransactionSearch_QNAME = new QName(
        "http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/ePayStatusSchema.xsd",
        "epayTransactionSearch"
    );

    public static JAXBElement<EpayTransactionSearch> wrapRequest(EpayTransactionSearch value) {
        return new JAXBElement<>(_EpayTransactionSearch_QNAME, EpayTransactionSearch.class, null, value);
    }
} 