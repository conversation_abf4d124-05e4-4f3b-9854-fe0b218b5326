package com.paycraft.rta.abt.cam.external_integration.config;

import brave.http.HttpServerRequest;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.paycraft.rta.abt.cam.common.domain.dtos.AppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.ValidationResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.ErrorCodesEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.LanguageEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.MessageKeyEnum;
import com.paycraft.rta.abt.cam.common.exception.CbtIntegrationException;
import com.paycraft.rta.abt.cam.common.exception.SanadIntegrationException;
import com.paycraft.rta.abt.cam.common.exception.ValidationException;
import com.paycraft.rta.abt.cam.common.utils.LocaleUtil;
import com.paycraft.rta.abt.cam.common.utils.MessageResolver;
import com.paycraft.rta.abt.cam.external_integration.domain.ExternalFaultCodeEnum;
import feign.FeignException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import static com.paycraft.rta.abt.cam.common.domain.enums.ErrorCodesEnum.SERVICE_CALL_ERROR;
import static com.paycraft.rta.abt.cam.external_integration.domain.constants.LogsConstant.externalService;

@RestControllerAdvice
public class GlobalExceptionHandler extends ResponseEntityExceptionHandler {

    private static final Logger log = LogManager.getLogger(GlobalExceptionHandler.class);

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private MessageResolver messageResolver;

    @Override
    protected ResponseEntity<Object> handleMethodArgumentNotValid(MethodArgumentNotValidException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        // Extract validation errors
        List<ValidationResponseDTO> errors = ex.getBindingResult()
                .getFieldErrors()
                .stream()
                .map(fieldError -> ValidationResponseDTO.of(
                        resolveMessageCode(fieldError).getErrorCode(),
                        resolveMessageCode(fieldError).getMessage() +" : "+ fieldError.getField()
                ))
                .collect(Collectors.toList());

        // For Custom Annotations
        errors.addAll(
                ex.getBindingResult()
                        .getGlobalErrors()
                        .stream()
                        .map(globalError -> ValidationResponseDTO.of(
                                ErrorCodesEnum.INVALID_REQUEST_DETAILS.getErrorCode(),
                                ErrorCodesEnum.INVALID_REQUEST_DETAILS.getMessage()+" : "+globalError.getDefaultMessage()
                        ))
                        .collect(Collectors.toList())
        );


        // Construct AppResponseDTO with validation errors
        AppResponseDTO response = new AppResponseDTO(
                MessageKeyEnum.INVALID_REQUEST.getCode(),
                MessageKeyEnum.INVALID_REQUEST.getMessage(),
                errors, // Passing list of validation errors
                null
        );
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }
//
    @ExceptionHandler(CbtIntegrationException.class)
    public ResponseEntity<AppResponseDTO> handleIntegrationException(CbtIntegrationException ex) {
        log.error("CbtIntegrationException: {}", ex.getResponseMessage());
        AppResponseDTO response = new AppResponseDTO(ex.getResponseCode(), ex.getResponseMessage() ,ex.getViolations(),null);
        return String.valueOf(SERVICE_CALL_ERROR.getErrorCode()).equals(ex.getResponseCode()) ? new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR) : new ResponseEntity<>(response, HttpStatus.OK);
}
    @ExceptionHandler(ValidationException.class)
    public ResponseEntity<AppResponseDTO<?>> handleValidationException(ValidationException ex, WebRequest request) {
        log.error("Inside handleValidationException for ValidationException {}",ex.getLocalizedMessage());
        MessageKeyEnum messageKey = MessageKeyEnum.VALIDATION_FAILED;
        List<ValidationResponseDTO> errors = ex.getValidationErrors();
        AppResponseDTO<?> response = AppResponseDTO.of(messageKey.getCode(),ex.getMessage(),errors);
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }
    @ExceptionHandler(SanadIntegrationException.class)
    public ResponseEntity<AppResponseDTO<?>> handleSanadException(ValidationException ex, WebRequest request) {
        log.error("Inside SanadException {}",ex.getLocalizedMessage());
        MessageKeyEnum messageKey = MessageKeyEnum.RESOURCE_NOT_FOUND;
        List<ValidationResponseDTO> errors = ex.getValidationErrors();
        AppResponseDTO<?> response = AppResponseDTO.of(messageKey.getCode(),ex.getMessage(),errors);
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(FeignException.class)
    public ResponseEntity<AppResponseDTO<?>> handleFeignException(FeignException ex , WebRequest request ) {
        String responseBody = ex.contentUTF8();
        log.error("Feign call failed. Status: {}, Response: {} ,Service Called:  {}", ex.status(), responseBody,externalService);
        String langHeader = request.getHeader("X-CLIENT-LANGUAGE");
        LanguageEnum clientLanguage = LanguageEnum.valueOf(langHeader!=null ? langHeader.toUpperCase():"EN");
        Locale locale = LocaleUtil.fromLanguageEnum(clientLanguage);
        ExternalFaultCodeEnum matchedError = null;

        try {
            JsonNode root = objectMapper.readTree(responseBody);

            // Case 1: fault-based response
            if (root.has("fault")) {
                String code = root.path("fault").path("code").asText();
                matchedError = ExternalFaultCodeEnum.fromFaultCode(code);
            }
            // Case 2: errorCode field
            else if (root.has("errorCode")) {
                String code = root.get("errorCode").asText();
                matchedError = ExternalFaultCodeEnum.fromFaultCode(code);
            }
            // Case 3: Custom Sanad error
            else if (root.has("ErrorDigitalSanadCards")) {
                JsonNode errors = root.get("ErrorDigitalSanadCards");
                if (errors.isArray() && errors.size() > 0) {
                    String msg = errors.get(0).path("ErrorMessage").asText();
                    if ("No Digital Sanad Card found.".equals(msg)) {
                        matchedError = ExternalFaultCodeEnum.NOT_FOUND;
                    }
                }
            }

        } catch (Exception parseEx) {
            log.warn("Could not parse error response body: {}", parseEx.getMessage());
        }

        // Fallback to Feign status mapping if no custom error found
        if (matchedError == null) {
            matchedError = ExternalFaultCodeEnum.fromFaultCode(String.valueOf(ex.status()));
        }

        // If still null → fallback internal server error
        if (matchedError == null) {
            matchedError = ExternalFaultCodeEnum.INTERNAL_SERVER_ERROR;
        }

        // Build response
        AppResponseDTO<?> appResponseDTO = AppResponseDTO.of(
                matchedError.getErrorCode(),
                messageResolver.resolveMessage(matchedError.getErrorCode(),locale),
                null
        );

        // Map enum → HttpStatus
        HttpStatus status;
        try {
            status = HttpStatus.valueOf(Integer.parseInt(matchedError.getCode()));
        } catch (Exception e) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
        }

        return new ResponseEntity<>(appResponseDTO, status);
    }
    private ErrorCodesEnum  resolveMessageCode(FieldError error) {
        return switch (Objects.requireNonNull(error.getCode())) {
            case "NotBlank" -> ErrorCodesEnum.NOT_BLANK;
            case "NotNull" -> ErrorCodesEnum.NOT_NULL;
            case "NotEmpty" -> ErrorCodesEnum.NOT_EMPTY;
            case "Size" -> ErrorCodesEnum.SIZE;
            case "Min" -> ErrorCodesEnum.MIN;
            case "Max" -> ErrorCodesEnum.MAX;
            case "Email" -> ErrorCodesEnum.EMAIL;
            case "Pattern" -> ErrorCodesEnum.PATTERN;
            case "Positive" -> ErrorCodesEnum.POSITIVE;
            case "PositiveOrZero" -> ErrorCodesEnum.POSITIVE_OR_ZERO;
            case "Negative" -> ErrorCodesEnum.NEGATIVE;
            case "NegativeOrZero" -> ErrorCodesEnum.NEGATIVE_OR_ZERO;
            case "Future" -> ErrorCodesEnum.FUTURE;
            case "FutureOrPresent" -> ErrorCodesEnum.FUTURE_OR_PRESENT;
            case "Past" -> ErrorCodesEnum.PAST;
            case "PastOrPresent" -> ErrorCodesEnum.PAST_OR_PRESENT;
            case "UniqueTransactionId" -> ErrorCodesEnum.UniqueTransactionId;
            case "DecimalMin" -> ErrorCodesEnum.DECIMAL_MIN;
            case "DecimalMax" -> ErrorCodesEnum.DECIMAL_MAX;
            case "Digits" -> ErrorCodesEnum.DIGITS;
            case "CreditCardNumber" -> ErrorCodesEnum.CREDIT_CARD_NUMBER;
            case "Length" -> ErrorCodesEnum.LENGTH;
            default -> ErrorCodesEnum.NOT_NULL;

        };
    }
}