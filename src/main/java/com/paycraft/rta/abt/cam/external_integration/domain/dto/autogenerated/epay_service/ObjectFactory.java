//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.0 
// See https://eclipse-ee4j.github.io/jaxb-ri 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2025.08.22 at 11:46:20 AM IST 
//


package com.paycraft.rta.abt.cam.external_integration.domain.dto.autogenerated.epay_service;

import javax.xml.namespace.QName;
import jakarta.xml.bind.JAXBElement;
import jakarta.xml.bind.annotation.XmlElementDecl;
import jakarta.xml.bind.annotation.XmlRegistry;

import java.lang.Error;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.paycraft.rta.abt.cam.external_integration.xml.epay_service.dto package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _ConfirmServiceDelivery_QNAME = new QName("http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/Schema.xsd", "confirmServiceDelivery");
    private final static QName _ConfirmServiceDeliveryResponse_QNAME = new QName("http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/Schema.xsd", "confirmServiceDeliveryResponse");
    private final static QName _Echo_QNAME = new QName("http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/Schema.xsd", "echo");
    private final static QName _EchoResponse_QNAME = new QName("http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/Schema.xsd", "echoResponse");
    private final static QName _GenerateTransactionToken_QNAME = new QName("http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/Schema.xsd", "generateTransactionToken");
    private final static QName _GenerateTransactionTokenResponse_QNAME = new QName("http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/Schema.xsd", "generateTransactionTokenResponse");
    private final static QName _GetResponseTokenDetails_QNAME = new QName("http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/Schema.xsd", "getResponseTokenDetails");
    private final static QName _GetResponseTokenDetailsResponse_QNAME = new QName("http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/Schema.xsd", "getResponseTokenDetailsResponse");
    private final static QName _FaultCode_QNAME = new QName("http://www.rta.ae/ActiveMatrix/ESB/ePayService/Fault/XMLSchema", "FaultCode");
    private final static QName _FaultString_QNAME = new QName("http://www.rta.ae/ActiveMatrix/ESB/ePayService/Fault/XMLSchema", "FaultString");
    private final static QName _FaultDetail_QNAME = new QName("http://www.rta.ae/ActiveMatrix/ESB/ePayService/Fault/XMLSchema", "FaultDetail");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.paycraft.rta.abt.cam.external_integration.xml.epay_service.dto
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link ConfirmServiceDelivery }
     * 
     */
    public ConfirmServiceDelivery createConfirmServiceDelivery() {
        return new ConfirmServiceDelivery();
    }

    /**
     * Create an instance of {@link ConfirmServiceDeliveryResponse }
     * 
     */
    public ConfirmServiceDeliveryResponse createConfirmServiceDeliveryResponse() {
        return new ConfirmServiceDeliveryResponse();
    }

    /**
     * Create an instance of {@link Echo }
     * 
     */
    public Echo createEcho() {
        return new Echo();
    }

    /**
     * Create an instance of {@link EchoResponse }
     * 
     */
    public EchoResponse createEchoResponse() {
        return new EchoResponse();
    }

    /**
     * Create an instance of {@link GenerateTransactionToken }
     * 
     */
    public GenerateTransactionToken createGenerateTransactionToken() {
        return new GenerateTransactionToken();
    }

    /**
     * Create an instance of {@link GenerateTransactionTokenResponse }
     * 
     */
    public GenerateTransactionTokenResponse createGenerateTransactionTokenResponse() {
        return new GenerateTransactionTokenResponse();
    }

    /**
     * Create an instance of {@link GetResponseTokenDetails }
     * 
     */
    public GetResponseTokenDetails createGetResponseTokenDetails() {
        return new GetResponseTokenDetails();
    }

    /**
     * Create an instance of {@link GetResponseTokenDetailsResponse }
     * 
     */
    public GetResponseTokenDetailsResponse createGetResponseTokenDetailsResponse() {
        return new GetResponseTokenDetailsResponse();
    }

    /**
     * Create an instance of {@link BeneficiaryInfo }
     * 
     */
    public BeneficiaryInfo createBeneficiaryInfo() {
        return new BeneficiaryInfo();
    }

    /**
     * Create an instance of {@link BeneficiaryInfos }
     * 
     */
    public BeneficiaryInfos createBeneficiaryInfos() {
        return new BeneficiaryInfos();
    }

    /**
     * Create an instance of {@link CompanyInfo }
     * 
     */
    public CompanyInfo createCompanyInfo() {
        return new CompanyInfo();
    }

    /**
     * Create an instance of {@link ServiceInfo }
     * 
     */
    public ServiceInfo createServiceInfo() {
        return new ServiceInfo();
    }

    /**
     * Create an instance of {@link ServiceInfos }
     * 
     */
    public ServiceInfos createServiceInfos() {
        return new ServiceInfos();
    }

    /**
     * Create an instance of {@link TransactionInfo }
     * 
     */
    public TransactionInfo createTransactionInfo() {
        return new TransactionInfo();
    }

    /**
     * Create an instance of {@link UserInfo }
     * 
     */
    public UserInfo createUserInfo() {
        return new UserInfo();
    }

    /**
     * Create an instance of {@link Fault }
     * 
     */
    public Fault createFault() {
        return new Fault();
    }

    /**
     * Create an instance of {@link Amount }
     * 
     */
    public Amount createAmount() {
        return new Amount();
    }

    /**
     * Create an instance of {@link Entry }
     * 
     */
    public Entry createEntry() {
        return new Entry();
    }

    /**
     * Create an instance of {@link java.lang.Error }
     * 
     */
    public java.lang.Error createError() {
        return new Error();
    }

    /**
     * Create an instance of {@link Map }
     * 
     */
    public Map createMap() {
        return new Map();
    }

    /**
     * Create an instance of {@link Message }
     * 
     */
    public Message createMessage() {
        return new Message();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConfirmServiceDelivery }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link ConfirmServiceDelivery }{@code >}
     */
    @XmlElementDecl(namespace = "http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/Schema.xsd", name = "confirmServiceDelivery")
    public JAXBElement<ConfirmServiceDelivery> createConfirmServiceDelivery(ConfirmServiceDelivery value) {
        return new JAXBElement<ConfirmServiceDelivery>(_ConfirmServiceDelivery_QNAME, ConfirmServiceDelivery.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConfirmServiceDeliveryResponse }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link ConfirmServiceDeliveryResponse }{@code >}
     */
    @XmlElementDecl(namespace = "http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/Schema.xsd", name = "confirmServiceDeliveryResponse")
    public JAXBElement<ConfirmServiceDeliveryResponse> createConfirmServiceDeliveryResponse(ConfirmServiceDeliveryResponse value) {
        return new JAXBElement<ConfirmServiceDeliveryResponse>(_ConfirmServiceDeliveryResponse_QNAME, ConfirmServiceDeliveryResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Echo }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link Echo }{@code >}
     */
    @XmlElementDecl(namespace = "http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/Schema.xsd", name = "echo")
    public JAXBElement<Echo> createEcho(Echo value) {
        return new JAXBElement<Echo>(_Echo_QNAME, Echo.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link EchoResponse }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link EchoResponse }{@code >}
     */
    @XmlElementDecl(namespace = "http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/Schema.xsd", name = "echoResponse")
    public JAXBElement<EchoResponse> createEchoResponse(EchoResponse value) {
        return new JAXBElement<EchoResponse>(_EchoResponse_QNAME, EchoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GenerateTransactionToken }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link GenerateTransactionToken }{@code >}
     */
    @XmlElementDecl(namespace = "http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/Schema.xsd", name = "generateTransactionToken")
    public JAXBElement<GenerateTransactionToken> createGenerateTransactionToken(GenerateTransactionToken value) {
        return new JAXBElement<GenerateTransactionToken>(_GenerateTransactionToken_QNAME, GenerateTransactionToken.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GenerateTransactionTokenResponse }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link GenerateTransactionTokenResponse }{@code >}
     */
    @XmlElementDecl(namespace = "http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/Schema.xsd", name = "generateTransactionTokenResponse")
    public JAXBElement<GenerateTransactionTokenResponse> createGenerateTransactionTokenResponse(GenerateTransactionTokenResponse value) {
        return new JAXBElement<GenerateTransactionTokenResponse>(_GenerateTransactionTokenResponse_QNAME, GenerateTransactionTokenResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GetResponseTokenDetails }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link GetResponseTokenDetails }{@code >}
     */
    @XmlElementDecl(namespace = "http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/Schema.xsd", name = "getResponseTokenDetails")
    public JAXBElement<GetResponseTokenDetails> createGetResponseTokenDetails(GetResponseTokenDetails value) {
        return new JAXBElement<GetResponseTokenDetails>(_GetResponseTokenDetails_QNAME, GetResponseTokenDetails.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GetResponseTokenDetailsResponse }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link GetResponseTokenDetailsResponse }{@code >}
     */
    @XmlElementDecl(namespace = "http://www.rta.ae/ActiveMatrix/ESB/ePayService/XMLSchema/Schema.xsd", name = "getResponseTokenDetailsResponse")
    public JAXBElement<GetResponseTokenDetailsResponse> createGetResponseTokenDetailsResponse(GetResponseTokenDetailsResponse value) {
        return new JAXBElement<GetResponseTokenDetailsResponse>(_GetResponseTokenDetailsResponse_QNAME, GetResponseTokenDetailsResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://www.rta.ae/ActiveMatrix/ESB/ePayService/Fault/XMLSchema", name = "FaultCode")
    public JAXBElement<String> createFaultCode(String value) {
        return new JAXBElement<String>(_FaultCode_QNAME, String.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://www.rta.ae/ActiveMatrix/ESB/ePayService/Fault/XMLSchema", name = "FaultString")
    public JAXBElement<String> createFaultString(String value) {
        return new JAXBElement<String>(_FaultString_QNAME, String.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://www.rta.ae/ActiveMatrix/ESB/ePayService/Fault/XMLSchema", name = "FaultDetail")
    public JAXBElement<String> createFaultDetail(String value) {
        return new JAXBElement<String>(_FaultDetail_QNAME, String.class, null, value);
    }

}
