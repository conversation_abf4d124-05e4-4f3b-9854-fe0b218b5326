//package com.paycraft.rta.abt.cam.external_integration.service.impl;
//
//import com.paycraft.rta.abt.cam.external_integration.config.SoapClientRegistry;
//import com.paycraft.rta.abt.cam.external_integration.xml.tibco_sms.dto.SMSServiceResponse;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//import org.springframework.ws.soap.client.core.SoapActionCallback;
//
//@Service
//public class SmsServiceImpl {
//
//    @Autowired
//    SoapClientRegistry soapClientRegistry;
//
//    public void sendSMS() {
//        SMSServiceResponse response = (SMSServiceResponse)
//                soapClientRegistry.getTemplate("tibco_sms").marshalSendAndReceive("https://eipstg.rtatestdom.local:11122/eProxy/service/SendSMSService",
//                        "",
//                        new SoapActionCallback("SendSMS"));
//
//        System.out.println("Status: " + response.getStatus());
//    }
//}
