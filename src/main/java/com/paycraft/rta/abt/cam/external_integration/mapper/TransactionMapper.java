package com.paycraft.rta.abt.cam.external_integration.mapper;

import com.paycraft.rta.abt.cam.common.domain.external_dtos.ePayTransactions.EPayTransactionSearchResponseDTO;
import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.Unmarshaller;
import lombok.extern.slf4j.Slf4j;

import java.io.StringReader;

@Slf4j
public class TransactionMapper {


    public static EPayTransactionSearchResponseDTO parse(String xml) throws Exception {
        JAXBContext jaxbContext = JAXBContext.newInstance(EPayTransactionSearchResponseDTO.class);
        log.info("Inside parsing method , JAXBContent object {} " , jaxbContext);
        Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();
        log.info("Inside parsing method ,  {} Unmarshaller " , unmarshaller);
        EPayTransactionSearchResponseDTO ePayTransactionSearchResponseDTO = (EPayTransactionSearchResponseDTO) unmarshaller.unmarshal(new StringReader(xml));
        log.info("Inside parsing method , Response Object {} " , ePayTransactionSearchResponseDTO);
        return ePayTransactionSearchResponseDTO;
    }
}
