package com.paycraft.rta.abt.cam.external_integration.service;

import com.paycraft.rta.abt.cam.common.domain.enums.LanguageEnum;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.ePayTransactions.EPayTransactionSearchResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.ePayTransactions.EpayStatusServiceRequestDTO;

public interface EpayTransactionService {

    EPayTransactionSearchResponseDTO searchEpayTransaction(EpayStatusServiceRequestDTO epayTransactionSearch, LanguageEnum clientLanauage);

    EPayTransactionSearchResponseDTO getTransactionStatus(EpayStatusServiceRequestDTO requestDTO, LanguageEnum clientLanauage);

    EPayTransactionSearchResponseDTO getDegTransactionStatus(EpayStatusServiceRequestDTO requestDTO, LanguageEnum clientLanauage);
}
