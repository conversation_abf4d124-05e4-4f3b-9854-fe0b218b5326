package com.paycraft.rta.abt.cam.external_integration.service.impl;

import com.paycraft.rta.abt.cam.common.domain.dtos.ValidationResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.CbtAbtEnabled;
import com.paycraft.rta.abt.cam.common.domain.enums.ErrorCodesEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.LanguageEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.MessageKeyEnum;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.ePayTransactions.ConfirmDeliveryResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.ePayTransactions.ConfirmServiceDeliveryRequestDTO;
import com.paycraft.rta.abt.cam.common.exception.CbtIntegrationException;
import com.paycraft.rta.abt.cam.common.repository.CamMstTableDetailsRepository;
import com.paycraft.rta.abt.cam.external_integration.config.SoapClientRegistry;
import com.paycraft.rta.abt.cam.external_integration.domain.dto.autogenerated.epay_service.*;
import com.paycraft.rta.abt.cam.external_integration.mapper.EPayTransactionMapper;
import com.paycraft.rta.abt.cam.external_integration.service.EPayService;
import com.paycraft.rta.abt.cam.external_integration.service.Utils.FaultParserUtil;
import jakarta.xml.bind.JAXBElement;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.ws.client.WebServiceClientException;
import org.springframework.ws.client.core.WebServiceTemplate;
import org.springframework.ws.soap.client.SoapFaultClientException;
import org.springframework.ws.soap.client.core.SoapActionCallback;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.paycraft.rta.abt.cam.external_integration.domain.constants.LogsConstant.externalApiHitTime;
import static com.paycraft.rta.abt.cam.external_integration.domain.constants.LogsConstant.externalApiResponseTime;

@Service
public class EpayServiceImpl implements EPayService {

    private static final Logger log = LogManager.getLogger(EpayServiceImpl.class);


    @Autowired
    SoapClientRegistry soapClientRegistry;

    public EpayServiceImpl(SoapClientRegistry soapClientRegistry) {
        this.template = soapClientRegistry.getTemplate("epay_service");
    }

    private final WebServiceTemplate template;

    @Value("${E_PAY_SERVICE_URL}")
    private String SERVICE_URL;

    @Autowired
    EPayTransactionMapper ePayTransactionMapper;

    @Autowired
    CamMstTableDetailsRepository camMstTableDetailsRepository;

    @Override
    public ConfirmDeliveryResponseDTO confirmServiceDelivery(ConfirmServiceDeliveryRequestDTO confirmServiceDeliveryRequestDTO, LanguageEnum clientLanauage) {
        log.info("Entered confirmServiceDelivery() with request: {}", confirmServiceDeliveryRequestDTO);

        if (CbtAbtEnabled.CBT_ENABLED.name().equals(camMstTableDetailsRepository.getCbtOrAbtMode())) {
            log.info("CBT mode is enabled. Proceeding with ePay service call.");
            SoapActionCallback callback = new SoapActionCallback("confirmServiceDelivery");

            ObjectFactory objectFactory = new ObjectFactory();

            try {
                ConfirmServiceDelivery confirmServiceDelivery = ePayTransactionMapper.toConfirmServiceDeliveryRequestDTO(confirmServiceDeliveryRequestDTO);
                log.info("Mapped ConfirmServiceDeliveryRequestDTO to ConfirmServiceDelivery: {}", confirmServiceDelivery);
                JAXBElement<ConfirmServiceDelivery> jaxbRequest = objectFactory.createConfirmServiceDelivery(confirmServiceDelivery);
                log.info("Created JAXBElement request for ConfirmServiceDelivery");
                externalApiHitTime= Instant.now();
                JAXBElement<ConfirmDeliveryResponseDTO> jaxbResponse =
                        (JAXBElement<ConfirmDeliveryResponseDTO>) template.marshalSendAndReceive(
                                SERVICE_URL, jaxbRequest,callback);
                externalApiResponseTime=Instant.now();
                log.info("Received response from ePay service: {}", jaxbResponse);

                // Extract CDATA-wrapped XML string from response
                Boolean responseXml = jaxbResponse.getValue().getValid();
                log.info("Extracted validity from response: {}", responseXml);
                return new ConfirmDeliveryResponseDTO(responseXml);

            } catch (SoapFaultClientException ex) {
                log.error("Caught SoapFaultClientException: {}", ex.getMessage(), ex);
                List<ValidationResponseDTO> violations = new ArrayList<>();
                String referenceId = null;

                Optional<Fault> faultInfoOpt = Optional.ofNullable(FaultParserUtil.parseFault(ex, Fault.class));
                log.info("Parsed fault info from exception: {}", faultInfoOpt);

                if (faultInfoOpt.isPresent()) {
                    Fault faultInfo = faultInfoOpt.get();
                    referenceId = faultInfo.getFaultCode();
                    log.info("Fault Code: {}", referenceId);
                    if (faultInfo.getFaultString() != null) {
                        violations.add(ValidationResponseDTO.of(
                                ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(),
                                faultInfo.getFaultString()
                        ));
                    }
                    if (faultInfo.getFaultDetail() != null && !faultInfo.getFaultDetail().isBlank()) {
                        violations.add(ValidationResponseDTO.of(
                                ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(),
                                "Detail: " + faultInfo.getFaultDetail()
                        ));
                    }
                }

                if (violations.isEmpty()) {
                    violations.add(ValidationResponseDTO.of(
                            ErrorCodesEnum.SERVICE_CALL_ERROR.getErrorCode(),
                            ErrorCodesEnum.SERVICE_CALL_ERROR.getMessage()
                    ));
                }

                throw new CbtIntegrationException(referenceId, violations, MessageKeyEnum.VALIDATION_FAILED.getCode(), MessageKeyEnum.VALIDATION_FAILED.getMessage());
            } catch (WebServiceClientException ex) {
                log.error("Web service error during ePay transaction search: {}", ex.getMessage(), ex);
            } catch (Exception ex) {
                log.error("Unexpected error during ePay transaction search: {}", ex.getMessage(), ex);
            }

            return null;
        }else {
            return new ConfirmDeliveryResponseDTO(true);
        }
    }

    public void  getTransactionToken(){
        log.info("Entered getTransactionToken()");

        GenerateTransactionToken request = new GenerateTransactionToken();

// Fill TransactionInfo
        TransactionInfo transactionInfo = new TransactionInfo();
        transactionInfo.setSpCode("RTA3");
        transactionInfo.setServCode("mGov");
        transactionInfo.setSptrn("ETFE407000000000047707140");

        Amount amount = new Amount();
        amount.setCurrency("AED");
        amount.setValue(BigDecimal.valueOf(200));
        transactionInfo.setAmount(amount);

        //transactionInfo.setTimestamp("2017-05-11T10:45:21.000+04:00");
        transactionInfo.setDescription("test");
        transactionInfo.setType(PaymentType.SALE);
        transactionInfo.setVersionCode("2.1");
        transactionInfo.setPaymentChannel("100");
        request.setTransactionInfo(transactionInfo);

// Fill UserInfo
        UserInfo userInfo = new UserInfo();
        userInfo.setIsAuthenticated(true);
        userInfo.setUserId("<EMAIL>");
        userInfo.setUserName("zhang jiabin");
        userInfo.setFullNameEn("zhang jiabin");
        request.setUserInfo(userInfo);

// Fill Service
        ServiceInfo service = new ServiceInfo();
        service.setServiceNameEn("Pay Parking Fees");
        service.setServiceNameAr("???? ???????");
        service.setServiceId("283");

// Beneficiary
        BeneficiaryInfo beneficiary = new BeneficiaryInfo();
        beneficiary.setAccountId("************");

        Amount txnAmount = new Amount();
        txnAmount.setCurrency("AED");
        txnAmount.setValue(BigDecimal.valueOf(200));
        beneficiary.setTxnAmount(txnAmount);

        beneficiary.setFullNameEn("Ahmed Elshaer");
        beneficiary.setMobileNo("************");
        beneficiary.setEmail("<EMAIL>");
        beneficiary.setEmiratesId("***************");
        beneficiary.setType(BeneficiaryType.INDIVIDUAL);

        service.getBeneficiaryInfos().getBeneficiaryInfo().add(beneficiary);

// Additional Params
        Entry entry = new Entry();
        entry.setKey("AppName");
        entry.setValue("RTA_Drivers_And_Vehicles");
        service.getAdditionalParams().getEntry().add(entry);

// Add service to serviceInfos
        ServiceInfos serviceInfos = new ServiceInfos();
        serviceInfos.getService().add(service);
        request.setServiceInfos(serviceInfos);
        log.info("Sending generateTransactionToken request to ePay service");

        GenerateTransactionTokenResponse generateTransactionTokenResponse = (GenerateTransactionTokenResponse) soapClientRegistry.getTemplate("epay_service").marshalSendAndReceive("https://eipstg.rtatestdom.local:12115/epayservice",
                request,
                new SoapActionCallback("generateTransactionToken"));

        System.out.println("Response :: "+generateTransactionTokenResponse.getUri());
    }



    public void getResponseTokenDetails(){
        log.info("Entered getResponseTokenDetails()");

        GetResponseTokenDetails request = new GetResponseTokenDetails();
        request.setResponseToken("CBE12418DF5B5DD658AD153667C7EA89327AAE44F6DDF03693E0ABEE0F4ABBFBE8C32998D6878CBCC5F161C38344CF85B76003E8EABDD7EF0BD86CFA63345E5CF849418B31C935B233F8BE71F21FA4DE081AA8A73FFBE59B0BE095710F7D630D");
        request.setSpCode("RTA3");
        request.setServCode("mGov");
        log.info("Sending confirmServiceDeliveryMock request to ePay service");

        GetResponseTokenDetailsResponse getResponseTokenDetailsResponse = (GetResponseTokenDetailsResponse) soapClientRegistry.getTemplate("epay_service").marshalSendAndReceive("https://eipstg.rtatestdom.local:12115/epayservice",
                request,
                new SoapActionCallback("getResponseTokenDetails"));

        System.out.println("Response :: "+getResponseTokenDetailsResponse.getDegTrn());

    }

    public void confirmServiceDeliveryMock(){
        ConfirmServiceDelivery request = new ConfirmServiceDelivery();
        request.setSpCode("RTA3");
        request.setServCode("mGov");
        request.setSptrn("ETFE407000000000047707140");

        ConfirmServiceDeliveryResponse confirmServiceDeliveryResponse = (ConfirmServiceDeliveryResponse) soapClientRegistry.getTemplate("epay_service").marshalSendAndReceive("https://eipstg.rtatestdom.local:12115/epayservice",
                request,
                new SoapActionCallback("confirmServiceDelivery"));

        System.out.println("Response :: "+confirmServiceDeliveryResponse.isValid());
    }
}
