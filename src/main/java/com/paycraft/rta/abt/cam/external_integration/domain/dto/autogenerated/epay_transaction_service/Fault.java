//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.0 
// See https://eclipse-ee4j.github.io/jaxb-ri 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2025.08.22 at 11:46:20 AM IST 
//


package com.paycraft.rta.abt.cam.external_integration.domain.dto.autogenerated.epay_transaction_service;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element ref="{http://www.rta.ae/ActiveMatrix/ESB/ePayStatusService/Fault/XMLSchema}FaultCode"/&gt;
 *         &lt;element ref="{http://www.rta.ae/ActiveMatrix/ESB/ePayStatusService/Fault/XMLSchema}FaultString"/&gt;
 *         &lt;element ref="{http://www.rta.ae/ActiveMatrix/ESB/ePayStatusService/Fault/XMLSchema}FaultDetail"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "faultCode",
    "faultString",
    "faultDetail"
})
@XmlRootElement(name = "Fault", namespace = "http://www.rta.ae/ActiveMatrix/ESB/ePayStatusService/Fault/XMLSchema")
public class Fault {

    @XmlElement(name = "FaultCode", namespace = "http://www.rta.ae/ActiveMatrix/ESB/ePayStatusService/Fault/XMLSchema", required = true)
    protected String faultCode;
    @XmlElement(name = "FaultString", namespace = "http://www.rta.ae/ActiveMatrix/ESB/ePayStatusService/Fault/XMLSchema", required = true)
    protected String faultString;
    @XmlElement(name = "FaultDetail", namespace = "http://www.rta.ae/ActiveMatrix/ESB/ePayStatusService/Fault/XMLSchema", required = true)
    protected String faultDetail;

    /**
     * Gets the value of the faultCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFaultCode() {
        return faultCode;
    }

    /**
     * Sets the value of the faultCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFaultCode(String value) {
        this.faultCode = value;
    }

    /**
     * Gets the value of the faultString property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFaultString() {
        return faultString;
    }

    /**
     * Sets the value of the faultString property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFaultString(String value) {
        this.faultString = value;
    }

    /**
     * Gets the value of the faultDetail property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFaultDetail() {
        return faultDetail;
    }

    /**
     * Sets the value of the faultDetail property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFaultDetail(String value) {
        this.faultDetail = value;
    }

}
