package com.paycraft.rta.abt.cam.external_integration.service.impl;

import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.CreateAwbRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.CreateAwbResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.LanguageEnum;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.emxIntegration.CancelShipmentResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.emxIntegration.CreateShipmentRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.emxIntegration.CreateShipmentResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.emxIntegration.TrackShipmentResponseDTO;
import com.paycraft.rta.abt.cam.external_integration.feign.EMXClient;
import com.paycraft.rta.abt.cam.external_integration.mapper.EmxMapper;
import com.paycraft.rta.abt.cam.external_integration.service.EMXIntegrationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

import static com.paycraft.rta.abt.cam.external_integration.domain.constants.LogsConstant.externalApiHitTime;
import static com.paycraft.rta.abt.cam.external_integration.domain.constants.LogsConstant.externalApiResponseTime;

@Service
public class EMXIntegrationServiceImpl implements EMXIntegrationService {

    @Autowired
    private EMXClient emxClient;
    @Autowired
    private EmxMapper emxMapper;

    @Override
    public CreateAwbResponseDTO createShipment(CreateAwbRequestDTO request , LanguageEnum clientLanguage) {
        CreateShipmentRequestDTO emxRequest = emxMapper.toEmxRequest(request);
        externalApiHitTime= Instant.now();
        CreateShipmentResponseDTO emxResponse = emxClient.createShipment(emxRequest);
        externalApiResponseTime= Instant.now();
        // check if data is coming.
        return Optional.ofNullable(emxResponse.getAwbNumber()).isPresent() ? emxMapper.toEmxAwbResponse(emxResponse) : null;
    }

    @Override
    public List<TrackShipmentResponseDTO> trackShipment(String awbNumber , LanguageEnum clientLanguage) {
        externalApiHitTime= Instant.now();
        List<TrackShipmentResponseDTO> responseDTOS = emxClient.trackShipment(awbNumber);
        externalApiResponseTime= Instant.now();
        return responseDTOS;
    }

    @Override
    public CancelShipmentResponseDTO cancelShipment(String awbNumber , LanguageEnum clientLanguage) {
        externalApiHitTime=Instant.now();
        CancelShipmentResponseDTO responseDTO = emxClient.cancelShipment(awbNumber);
        externalApiResponseTime=Instant.now();
        return responseDTO;
    }
}
