package com.paycraft.rta.abt.cam.external_integration.mapper;

import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.pcard.SanadDTO;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.sanad.SanadCardDto;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;

@Mapper(componentModel = "spring")
public interface SanadCardMapper {

    @Mappings({
            @Mapping(source = "cardNo", target = "conNum"),
            @Mapping(source = "categoryEn", target = "disabilityCategory"),
            @Mapping(source = "digitalSanadCardTypeEn", target = "disabilityType"),
            @Mapping(source = "mocdpodNumber", target = "mocdPODNumber"),
            @Mapping(source = "mocdpodNumber", target = "podNumber"),
            @Mapping(source = "devicesAssistiveEn", target = "sanadSpcCard"),
            @Mapping(source = "expiryDate", target = "authConcessionExpireDate", qualifiedByName = "stringToLocalDate"),
            @Mapping(source = "nationalityEnglish", target = "nationalSocialCustomer")
    })
    SanadDTO toSanadDTO(SanadCardDto sanadCardDto);

    @Named("stringToLocalDate")
    default LocalDate stringToLocalDate(String date) {
        if (date == null || date.isEmpty()) return null;
        return OffsetDateTime.parse(date).toLocalDate();
    }
}
