package com.paycraft.rta.abt.cam.external_integration.config;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Base64;
import java.util.List;

@Slf4j
@Component
public class FeignRequestLogger implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate template) {
        log.info("Feign request: {} {}", template.method(), template.url());
        // Decode Authorization header if present
        String encodedAuth = template.headers().getOrDefault("Authorization", List.of())
                .stream()
                .findFirst()
                .orElse(null);

        if (encodedAuth != null && encodedAuth.startsWith("Basic ")) {
            String base64Part = encodedAuth.substring("Basic ".length());
            try {
                String decoded = new String(Base64.getDecoder().decode(base64Part));
                log.info("Authorization (decoded): {}", decoded); // prints: username:password
            } catch (IllegalArgumentException e) {
                log.warn("Failed to decode Authorization header", e);
            }
        }
        log.info("Headers: {}", template.headers());
        log.info("Body: {}", template.requestBody().asString());
    }
}
