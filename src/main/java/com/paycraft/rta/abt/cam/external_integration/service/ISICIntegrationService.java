package com.paycraft.rta.abt.cam.external_integration.service;

import com.paycraft.rta.abt.cam.common.domain.dtos.tibco.pcard.IsicDetailsDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.LanguageEnum;
import com.paycraft.rta.abt.cam.common.domain.external_dtos.isic.*;

public interface ISICIntegrationService {

    IsicDetailsDTO createOrUpdate(CardHolderSetRequestDTO requestDTO, LanguageEnum clientLanauage );

    CardReportResponseDTO cardReport(CardReportRequestDTO requestDTO, LanguageEnum clientLanauage);

    CardReportResponseDTO changeCardStatus(UpdateCardStatusAndValidityDTO requestDTO ,LanguageEnum clientLanauage);

    CardReportResponseDTO changeCardValidity(UpdateCardStatusAndValidityDTO requestDTO, LanguageEnum clientLanauage);
}
