package com.paycraft.rta.abt.cam.external_integration.service.Utils;

import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBElement;
import jakarta.xml.bind.Unmarshaller;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ws.soap.client.SoapFaultClientException;

import javax.xml.transform.Source;

public final class FaultParserUtil {

    private static final Logger log = LoggerFactory.getLogger(FaultParserUtil.class);

    private FaultParserUtil() {} // prevent instantiation

    /**
     * Generic fault parser: parses the SOAP fault detail into the given class.
     *
     * @param ex        the SoapFaultClientException
     * @param clazz     the Fault class type to parse into
     * @return          parsed object of type T, or null if parsing fails
     */
    public static <T> T parseFault(SoapFaultClientException ex, Class<T> clazz) {
        try {
            if (ex.getSoapFault() != null && ex.getSoapFault().getFaultDetail() != null) {
                Source detailSource = ex.getSoapFault().getFaultDetail().getSource();

                JAXBContext jaxbContext = JAXBContext.newInstance(clazz);
                Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();

                JAXBElement<T> jaxbElement = unmarshaller.unmarshal(detailSource, clazz);
                return jaxbElement.getValue();
            }
        } catch (Exception e) {
            log.error("Failed to parse SOAP Fault detail into {}", clazz.getSimpleName(), e);
        }
        return null;
    }
}
